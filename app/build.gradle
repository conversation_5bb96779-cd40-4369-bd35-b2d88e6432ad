plugins {
    id 'com.android.application'
}

android {
    namespace 'com.oriondev.luatdansu'
    compileSdk 34

    defaultConfig {
        applicationId "com.oriondev.luatdansu"
        minSdk 24
        targetSdk 34
        versionCode 5
        versionName "3.0"

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }

    buildTypes {
        release {
            minifyEnabled true
            shrinkResources true
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
}

dependencies {
    implementation 'androidx.appcompat:appcompat:1.6.1'
    implementation 'com.google.android.material:material:1.11.0'
    implementation 'androidx.constraintlayout:constraintlayout:2.1.4'
    implementation 'androidx.drawerlayout:drawerlayout:1.2.0'
    implementation 'androidx.recyclerview:recyclerview:1.3.2'
    implementation 'androidx.core:core-splashscreen:1.0.1'
    implementation 'com.google.android.gms:play-services-ads:23.0.0'
    implementation 'com.google.android.ump:user-messaging-platform:2.1.0'

    // Room Database
    implementation 'androidx.room:room-runtime:2.5.0'
    annotationProcessor 'androidx.room:room-compiler:2.5.0'

    // ViewModel and LiveData
    implementation 'androidx.lifecycle:lifecycle-viewmodel:2.7.0'
    implementation 'androidx.lifecycle:lifecycle-livedata:2.7.0'
    implementation 'androidx.lifecycle:lifecycle-common-java8:2.7.0'

    // JSON parsing
    implementation 'com.google.code.gson:gson:2.10.1'

    // Text formatting and search
    implementation 'androidx.core:core-ktx:1.12.0'

    testImplementation 'junit:junit:4.13.2'
    androidTestImplementation 'androidx.test.ext:junit:1.1.5'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.5.1'
}
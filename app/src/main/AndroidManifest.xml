<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="com.oriondev.luatdansu">

    <!-- <PERSON><PERSON><PERSON> quyền truy cập Internet -->
    <uses-permission android:name="android.permission.INTERNET"/>
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="com.google.android.gms.permission.AD_ID"/>

    <application
        android:allowBackup="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="Bộ Luật Dân Sự"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/Theme.LuatDanSu"
        tools:targetApi="31">

        <!-- <PERSON><PERSON> b<PERSON><PERSON> AdMob App ID -->
        <meta-data
            android:name="com.google.android.gms.ads.APPLICATION_ID"
            android:value="@string/admob_app_id"/>

        <activity
            android:name=".ChaptersActivity"
            android:exported="false"
            android:parentActivityName=".MainActivity"
            android:theme="@style/Theme.LuatDanSu" />

        <activity
            android:name=".SearchActivity"
            android:exported="false"
            android:parentActivityName=".MainActivity"
            android:theme="@style/Theme.LuatDanSu"
            android:launchMode="singleTop">
            <intent-filter>
                <action android:name="android.intent.action.SEARCH" />
            </intent-filter>
            <meta-data android:name="android.app.searchable"
                android:resource="@xml/searchable"/>
        </activity>

        <activity
            android:name=".BookmarkActivity"
            android:exported="false"
            android:parentActivityName=".MainActivity"
            android:theme="@style/Theme.LuatDanSu" />

        <!-- MainActivity with native implementation -->
        <activity
            android:name=".MainActivity"
            android:exported="true"
            android:configChanges="orientation|screenSize"
            android:theme="@style/Theme.LuatDanSu">
            <intent-filter>
                <action android:name="android.intent.action.MAIN"/>
                <category android:name="android.intent.category.LAUNCHER"/>
            </intent-filter>
        </activity>

        <!-- Test Data Activity -->
        <activity
            android:name=".TestDataActivity"
            android:exported="false"
            android:theme="@style/Theme.LuatDanSu" />

    </application>

</manifest>

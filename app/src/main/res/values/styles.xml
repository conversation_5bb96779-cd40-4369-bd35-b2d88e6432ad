<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- Base application theme -->
    <style name="AppTheme" parent="Theme.MaterialComponents.Light.NoActionBar">
        <!-- Primary brand color -->
        <item name="colorPrimary">@color/apple_blue</item>
        <item name="colorPrimaryVariant">@color/apple_blue_dark</item>
        <item name="colorOnPrimary">@color/white</item>
        
        <!-- Secondary brand color -->
        <item name="colorSecondary">@color/apple_gray</item>
        <item name="colorSecondaryVariant">@color/apple_gray_dark</item>
        <item name="colorOnSecondary">@color/black</item>
        
        <!-- Status bar color -->
        <item name="android:statusBarColor">@color/apple_blue</item>
        <item name="android:windowLightStatusBar">false</item>
        
        <!-- Text styles -->
        <item name="android:textViewStyle">@style/Widget.AppCompat.TextView.Apple</item>
        <item name="android:buttonStyle">@style/Widget.AppCompat.Button.Apple</item>
    </style>

    <!-- Apple-style text view -->
    <style name="Widget.AppCompat.TextView.Apple" parent="Widget.AppCompat.TextView">
        <item name="android:fontFamily">sans-serif</item>
        <item name="android:textColor">@color/black</item>
        <item name="android:textSize">17sp</item>
        <item name="android:lineSpacingMultiplier">1.2</item>
    </style>

    <!-- Apple-style button -->
    <style name="Widget.AppCompat.Button.Apple" parent="Widget.AppCompat.Button">
        <item name="android:fontFamily">sans-serif</item>
        <item name="android:textColor">@color/apple_blue</item>
        <item name="android:textSize">17sp</item>
        <item name="android:background">@drawable/apple_button_background</item>
        <item name="android:padding">12dp</item>
    </style>
</resources> 
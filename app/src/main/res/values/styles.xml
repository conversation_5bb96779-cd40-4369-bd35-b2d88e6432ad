<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- Base application theme -->
    <style name="AppTheme" parent="Theme.Material3.DayNight.NoActionBar">
        <!-- Primary brand color -->
        <item name="colorPrimary">@color/md_theme_light_primary</item>
        <item name="colorOnPrimary">@color/md_theme_light_onPrimary</item>
        <item name="colorPrimaryContainer">@color/md_theme_light_primaryContainer</item>
        <item name="colorOnPrimaryContainer">@color/md_theme_light_onPrimaryContainer</item>

        <!-- Secondary brand color -->
        <item name="colorSecondary">@color/md_theme_light_secondary</item>
        <item name="colorOnSecondary">@color/md_theme_light_onSecondary</item>
        <item name="colorSecondaryContainer">@color/md_theme_light_secondaryContainer</item>
        <item name="colorOnSecondaryContainer">@color/md_theme_light_onSecondaryContainer</item>

        <!-- Background colors -->
        <item name="android:colorBackground">@color/md_theme_light_background</item>
        <item name="colorOnBackground">@color/md_theme_light_onBackground</item>
        <item name="colorSurface">@color/md_theme_light_surface</item>
        <item name="colorOnSurface">@color/md_theme_light_onSurface</item>

        <!-- Status bar color -->
        <item name="android:statusBarColor">@color/md_theme_light_primary</item>
        <item name="android:windowLightStatusBar">true</item>
    </style>

    <!-- Modern text view -->
    <style name="Widget.AppCompat.TextView.Modern" parent="Widget.AppCompat.TextView">
        <item name="android:fontFamily">sans-serif</item>
        <item name="android:textColor">?attr/colorOnSurface</item>
        <item name="android:textSize">16sp</item>
        <item name="android:lineSpacingMultiplier">1.4</item>
    </style>

    <!-- Modern button -->
    <style name="Widget.AppCompat.Button.Modern" parent="Widget.Material3.Button">
        <item name="android:fontFamily">sans-serif-medium</item>
        <item name="android:textColor">?attr/colorOnPrimary</item>
        <item name="android:textSize">14sp</item>
        <item name="backgroundTint">?attr/colorPrimary</item>
        <item name="android:padding">12dp</item>
        <item name="cornerRadius">8dp</item>
    </style>
</resources>
<?xml version="1.0" encoding="utf-8"?>
<androidx.drawerlayout.widget.DrawerLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/drawer_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true"
    tools:context=".MainActivityNew">

    <!-- Main content -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <!-- App Bar -->
        <com.google.android.material.appbar.AppBarLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar">

            <com.google.android.material.appbar.MaterialToolbar
                android:id="@+id/toolbar"
                android:layout_width="match_parent"
                android:layout_height="?attr/actionBarSize"
                android:background="?attr/colorPrimary"
                app:popupTheme="@style/ThemeOverlay.AppCompat.Light"
                app:title="@string/app_name"
                app:titleTextColor="@android:color/white" />

            <!-- Search View -->
            <androidx.appcompat.widget.SearchView
                android:id="@+id/search_view"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_margin="8dp"
                android:background="@drawable/search_background"
                android:visibility="gone"
                app:iconifiedByDefault="false"
                app:queryHint="@string/search_hint" />

        </com.google.android.material.appbar.AppBarLayout>

        <!-- Progress Bar -->
        <com.google.android.material.progressindicator.LinearProgressIndicator
            android:id="@+id/progressBar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:indeterminate="true"
            android:visibility="gone"
            app:indicatorColor="?attr/colorPrimary" />

        <!-- Main Content Area -->
        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1">

            <!-- RecyclerView for Law Content -->
            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recycler_view"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:clipToPadding="false"
                android:paddingBottom="80dp"
                android:scrollbars="vertical"
                tools:listitem="@layout/item_law_article" />

            <!-- Empty State View (optional) -->
            <LinearLayout
                android:id="@+id/empty_state"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:gravity="center"
                android:orientation="vertical"
                android:visibility="gone">

                <ImageView
                    android:layout_width="120dp"
                    android:layout_height="120dp"
                    android:alpha="0.5"
                    android:src="@drawable/ic_search"
                    app:tint="?attr/colorOnSurfaceVariant" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:text="Không tìm thấy kết quả"
                    android:textAppearance="@style/TextAppearance.Material3.TitleMedium"
                    android:textColor="?attr/colorOnSurfaceVariant" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:text="Thử tìm kiếm với từ khóa khác"
                    android:textAppearance="@style/TextAppearance.Material3.BodyMedium"
                    android:textColor="?attr/colorOnSurfaceVariant" />

            </LinearLayout>

        </FrameLayout>

        <!-- Ad Container -->
        <com.google.android.material.card.MaterialCardView
            android:id="@+id/adContainer"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="8dp"
            android:visibility="gone"
            app:cardCornerRadius="8dp"
            app:cardElevation="2dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="8dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:text="@string/ad_label"
                    android:textAppearance="@style/TextAppearance.Material3.LabelSmall"
                    android:textColor="?attr/colorOnSurfaceVariant" />

                <com.google.android.gms.ads.AdView
                    android:id="@+id/adView"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:layout_marginTop="4dp"
                    app:adSize="BANNER"
                    app:adUnitId="@string/admob_banner_id" />

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- Bottom Navigation -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="?attr/colorSurface"
            android:elevation="8dp"
            android:orientation="horizontal"
            android:padding="8dp">

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btnSearch"
                style="@style/Widget.Material3.Button.IconButton"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                app:icon="@drawable/ic_search"
                app:iconGravity="top"
                app:iconSize="24dp" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btnHome"
                style="@style/Widget.Material3.Button.IconButton"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                app:icon="@drawable/ic_home"
                app:iconGravity="top"
                app:iconSize="24dp" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btnBookmark"
                style="@style/Widget.Material3.Button.IconButton"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                app:icon="@drawable/ic_bookmark"
                app:iconGravity="top"
                app:iconSize="24dp" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btnShare"
                style="@style/Widget.Material3.Button.IconButton"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                app:icon="@drawable/ic_share"
                app:iconGravity="top"
                app:iconSize="24dp" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btnSettings"
                style="@style/Widget.Material3.Button.IconButton"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                app:icon="@drawable/ic_settings"
                app:iconGravity="top"
                app:iconSize="24dp" />

        </LinearLayout>

    </LinearLayout>

    <!-- Navigation Drawer -->
    <com.google.android.material.navigation.NavigationView
        android:id="@+id/navigation_view"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:layout_gravity="start"
        android:fitsSystemWindows="true"
        app:headerLayout="@layout/nav_header"
        app:menu="@menu/nav_menu" />

</androidx.drawerlayout.widget.DrawerLayout>

<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="@dimen/nav_header_height"
    android:background="?attr/colorPrimaryContainer"
    android:gravity="bottom"
    android:orientation="vertical"
    android:paddingLeft="@dimen/activity_horizontal_margin"
    android:paddingTop="@dimen/activity_vertical_margin"
    android:paddingRight="@dimen/activity_horizontal_margin"
    android:paddingBottom="@dimen/activity_vertical_margin"
    android:theme="@style/ThemeOverlay.AppCompat.Dark">

    <ImageView
        android:id="@+id/nav_header_image"
        android:layout_width="80dp"
        android:layout_height="80dp"
        android:contentDescription="@string/app_name"
        android:paddingTop="@dimen/nav_header_vertical_spacing"
        app:srcCompat="@mipmap/ic_launcher_round" />

    <TextView
        android:id="@+id/nav_header_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingTop="@dimen/nav_header_vertical_spacing"
        android:text="@string/app_name"
        android:textAppearance="@style/TextAppearance.AppCompat.Body1"
        android:textColor="?attr/colorOnPrimaryContainer"
        android:textSize="18sp"
        android:textStyle="bold" />

    <TextView
        android:id="@+id/nav_header_subtitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Tra cứu luật dân sự nhanh chóng"
        android:textAppearance="@style/TextAppearance.AppCompat.Body2"
        android:textColor="?attr/colorOnPrimaryContainer"
        android:textSize="14sp" />
        
    <TextView
        android:id="@+id/nav_header_disclaimer"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:text="@string/disclaimer"
        android:textAppearance="@style/TextAppearance.AppCompat.Caption"
        android:textColor="?attr/colorOnPrimaryContainer"
        android:textSize="10sp"
        android:textStyle="italic" />

</LinearLayout>

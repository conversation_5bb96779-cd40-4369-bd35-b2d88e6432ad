<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/card_view"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="8dp"
    app:cardCornerRadius="12dp"
    app:cardElevation="4dp"
    app:strokeWidth="1dp"
    app:strokeColor="?attr/colorOutline">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <TextView
            android:id="@+id/label_text"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Label"
            android:textSize="14sp"
            android:textStyle="bold"
            android:textColor="?attr/colorPrimary"
            android:layout_marginBottom="4dp" />

        <TextView
            android:id="@+id/value_text"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Value"
            android:textSize="16sp"
            android:textColor="?attr/colorOnSurface"
            android:lineSpacingExtra="2dp" />

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>

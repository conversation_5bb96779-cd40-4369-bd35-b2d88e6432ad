<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    tools:context=".SearchActivity">

    <!-- App Bar -->
    <com.google.android.material.appbar.AppBarLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar">

        <com.google.android.material.appbar.MaterialToolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="?attr/colorPrimary"
            app:popupTheme="@style/ThemeOverlay.AppCompat.Light"
            app:title="Tìm kiếm"
            app:titleTextColor="@android:color/white" />

        <!-- Custom Search Layout -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="8dp"
            android:orientation="horizontal"
            android:background="@drawable/search_background"
            android:gravity="center_vertical">

            <EditText
                android:id="@+id/search_edit_text"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:layout_marginStart="16dp"
                android:layout_marginEnd="8dp"
                android:background="@android:color/transparent"
                android:hint="@string/search_hint"
                android:textColor="@color/search_text"
                android:textColorHint="@color/search_hint"
                android:textSize="16sp"
                android:maxLines="1"
                android:imeOptions="actionSearch"
                android:inputType="text" />

            <ImageButton
                android:id="@+id/search_clear"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:layout_marginEnd="8dp"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:src="@drawable/ic_close"
                android:tint="@color/search_icon"
                android:contentDescription="Xóa"
                android:visibility="gone" />

        </LinearLayout>

    </com.google.android.material.appbar.AppBarLayout>

    <!-- Progress Bar -->
    <com.google.android.material.progressindicator.LinearProgressIndicator
        android:id="@+id/progress_bar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:indeterminate="true"
        android:visibility="gone"
        app:indicatorColor="?attr/colorPrimary" />

    <!-- Content Area -->
    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <!-- Search Results -->
        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recycler_view"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:clipToPadding="false"
            android:padding="8dp"
            android:scrollbars="vertical"
            android:visibility="gone"
            tools:listitem="@layout/item_law_article" />

        <!-- Empty State -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:gravity="center"
            android:orientation="vertical"
            android:padding="32dp">

            <TextView
                android:id="@+id/empty_state_text"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:gravity="center"
                android:text="Nhập từ khóa để tìm kiếm"
                android:textAppearance="@style/TextAppearance.Material3.TitleMedium"
                android:textColor="?attr/colorOnSurfaceVariant" />

            <!-- Notification text disabled per user request -->
            <!--
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:gravity="center"
                android:text="Tìm kiếm trong toàn bộ nội dung Bộ Luật Dân sự"
                android:textAppearance="@style/TextAppearance.Material3.BodyMedium"
                android:textColor="?attr/colorOnSurfaceVariant" />
            -->

        </LinearLayout>

    </FrameLayout>

</LinearLayout>

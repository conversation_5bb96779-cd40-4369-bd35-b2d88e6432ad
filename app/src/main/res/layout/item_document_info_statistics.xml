<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/card_view"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="8dp"
    app:cardCornerRadius="12dp"
    app:cardElevation="4dp"
    app:strokeWidth="1dp"
    app:strokeColor="?attr/colorSecondary">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <TextView
            android:id="@+id/title_text"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Thống kê cấu trúc"
            android:textSize="18sp"
            android:textStyle="bold"
            android:textColor="?attr/colorSecondary"
            android:layout_marginBottom="12dp"
            android:drawableStart="@drawable/ic_analytics"
            android:drawablePadding="8dp"
            android:gravity="center_vertical" />

        <!-- Statistics grid -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <!-- Row 1 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginBottom="8dp">

                <TextView
                    android:id="@+id/chapter_count_text"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="Số chương: 0"
                    android:textSize="16sp"
                    android:textColor="?attr/colorOnSurface"
                    android:drawableStart="@drawable/ic_chapter"
                    android:drawablePadding="8dp"
                    android:gravity="center_vertical"
                    android:padding="8dp"
                    android:background="@drawable/bg_stat_item" />

                <TextView
                    android:id="@+id/article_count_text"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:layout_marginStart="8dp"
                    android:text="Số điều khoản: 0"
                    android:textSize="16sp"
                    android:textColor="?attr/colorOnSurface"
                    android:drawableStart="@drawable/ic_article"
                    android:drawablePadding="8dp"
                    android:gravity="center_vertical"
                    android:padding="8dp"
                    android:background="@drawable/bg_stat_item" />

            </LinearLayout>

            <!-- Row 2 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/clause_count_text"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="Số khoản: 0"
                    android:textSize="16sp"
                    android:textColor="?attr/colorOnSurface"
                    android:drawableStart="@drawable/ic_clause"
                    android:drawablePadding="8dp"
                    android:gravity="center_vertical"
                    android:padding="8dp"
                    android:background="@drawable/bg_stat_item" />

                <TextView
                    android:id="@+id/total_items_text"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:layout_marginStart="8dp"
                    android:text="Tổng số mục: 0"
                    android:textSize="16sp"
                    android:textColor="?attr/colorOnSurface"
                    android:drawableStart="@drawable/ic_total"
                    android:drawablePadding="8dp"
                    android:gravity="center_vertical"
                    android:padding="8dp"
                    android:background="@drawable/bg_stat_item" />

            </LinearLayout>

        </LinearLayout>

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>

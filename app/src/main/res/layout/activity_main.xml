<?xml version="1.0" encoding="utf-8"?>
<androidx.drawerlayout.widget.DrawerLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/drawer_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true"
    tools:context=".MainActivity">

    <!-- Main content layout -->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/main"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/background_light">

        <!-- Toolbar -->
        <com.google.android.material.appbar.MaterialToolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="@color/nav_green"
            android:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:title="@string/app_name"
            app:titleTextColor="@android:color/white" />

        <!-- Search Container -->
        <LinearLayout
            android:id="@+id/search_container"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:padding="16dp"
            android:background="@color/search_background"
            android:visibility="gone"
            app:layout_constraintTop_toBottomOf="@id/toolbar"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent">

            <EditText
                android:id="@+id/search_edit_text"
                android:layout_width="0dp"
                android:layout_height="48dp"
                android:layout_weight="1"
                android:background="@drawable/search_background"
                android:hint="Tìm kiếm nội dung luật..."
                android:textColorHint="@color/search_hint"
                android:textColor="@color/search_text"
                android:paddingStart="16dp"
                android:paddingEnd="16dp"
                android:imeOptions="actionSearch"
                android:inputType="text" />

            <ImageButton
                android:id="@+id/search_button"
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:layout_marginStart="8dp"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:src="@drawable/ic_search"
                android:contentDescription="Tìm kiếm"
                android:visibility="gone" />

            <ImageButton
                android:id="@+id/search_clear"
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:layout_marginStart="8dp"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:src="@drawable/ic_clear"
                android:contentDescription="Xóa"
                android:visibility="gone" />

        </LinearLayout>

        <!-- Progress Bar -->
        <com.google.android.material.progressindicator.LinearProgressIndicator
            android:id="@+id/progressBar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:indeterminate="true"
            android:visibility="gone"
            app:indicatorColor="@color/nav_green"
            app:layout_constraintTop_toBottomOf="@id/search_container"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />

        <!-- Disclaimer Banner -->
        <com.google.android.material.card.MaterialCardView
            android:id="@+id/disclaimerBanner"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="16dp"
            app:cardCornerRadius="16dp"
            app:cardElevation="4dp"
            app:strokeWidth="1dp"
            app:strokeColor="@color/apple_gray"
            app:layout_constraintTop_toBottomOf="@id/progressBar"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Tuyên bố miễn trừ trách nhiệm"
                    android:textStyle="bold"
                    android:textSize="16sp"
                    android:textColor="@color/text_primary" />

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:text="Ứng dụng này không phải là ứng dụng chính thức của chính phủ và không liên kết với bất kỳ cơ quan chính phủ nào. Nội dung được cung cấp chỉ nhằm mục đích tham khảo."
                    android:textSize="14sp"
                    android:textColor="@color/text_secondary" />

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- RecyclerView hiển thị nội dung luật - NATIVE ONLY -->
        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recyclerView"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:background="@color/background_light"
            android:layout_marginTop="8dp"
            android:fitsSystemWindows="true"
            app:layout_constraintTop_toBottomOf="@id/disclaimerBanner"
            app:layout_constraintBottom_toTopOf="@id/bottom_navigation"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"/>

        <!-- Bottom Navigation -->
        <LinearLayout
            android:id="@+id/bottom_navigation"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:background="@color/nav_green"
            android:padding="8dp"
            app:layout_constraintBottom_toTopOf="@id/adContainer"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent">

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btnHome"
                style="@style/Widget.Material3.Button.TextButton"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="Trang chủ"
                android:textColor="@android:color/white"
                android:drawableTop="@drawable/ic_home"
                android:drawableTint="@android:color/white" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btnSearch"
                style="@style/Widget.Material3.Button.TextButton"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="Tìm kiếm"
                android:textColor="@android:color/white"
                android:drawableTop="@drawable/ic_search"
                android:drawableTint="@android:color/white" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btnBookmark"
                style="@style/Widget.Material3.Button.TextButton"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="Bookmark"
                android:textColor="@android:color/white"
                android:drawableTop="@drawable/ic_bookmark"
                android:drawableTint="@android:color/white" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btnShare"
                style="@style/Widget.Material3.Button.TextButton"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="Chia sẻ"
                android:textColor="@android:color/white"
                android:drawableTop="@drawable/ic_share"
                android:drawableTint="@android:color/white" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btnSettings"
                style="@style/Widget.Material3.Button.TextButton"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="Cài đặt"
                android:textColor="@android:color/white"
                android:drawableTop="@drawable/ic_settings"
                android:drawableTint="@android:color/white" />

        </LinearLayout>

        <!-- AdMob Container -->
        <com.google.android.material.card.MaterialCardView
            android:id="@+id/adContainer"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="8dp"
            app:cardCornerRadius="8dp"
            app:cardElevation="2dp"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent">

            <LinearLayout
                android:id="@+id/adViewContainer"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:gravity="center" />

        </com.google.android.material.card.MaterialCardView>

    </androidx.constraintlayout.widget.ConstraintLayout>

    <!-- Navigation Drawer -->
    <com.google.android.material.navigation.NavigationView
        android:id="@+id/navigation_view"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:layout_gravity="start"
        android:fitsSystemWindows="true"
        app:headerLayout="@layout/nav_header"
        app:menu="@menu/activity_main_drawer" />

</androidx.drawerlayout.widget.DrawerLayout>

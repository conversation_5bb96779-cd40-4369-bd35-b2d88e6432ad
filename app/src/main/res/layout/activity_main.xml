<?xml version="1.0" encoding="utf-8"?>
<androidx.drawerlayout.widget.DrawerLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/drawer_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true"
    tools:context=".MainActivity">

    <!-- Main Content with Beautiful Card-based Design -->
    <androidx.coordinatorlayout.widget.CoordinatorLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="#F5F7FA">

        <!-- App Bar Layout with Modern Design -->
        <com.google.android.material.appbar.AppBarLayout
            android:id="@+id/app_bar_layout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@android:color/transparent"
            app:elevation="0dp">

            <com.google.android.material.appbar.MaterialToolbar
                android:id="@+id/toolbar"
                android:layout_width="match_parent"
                android:layout_height="64dp"
                android:background="@drawable/gradient_primary"
                android:elevation="0dp"
                app:title="@string/app_name"
                app:titleTextColor="@android:color/white"
                app:titleTextAppearance="@style/ToolbarTitleStyle"
                app:navigationIcon="@drawable/ic_menu"
                app:navigationIconTint="@android:color/white"
                app:layout_scrollFlags="scroll|enterAlways" />

        </com.google.android.material.appbar.AppBarLayout>

        <!-- Main Content ScrollView -->
        <androidx.core.widget.NestedScrollView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:fillViewport="true"
            app:layout_behavior="@string/appbar_scrolling_view_behavior">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="24dp">

                <!-- Search Card -->
                <com.google.android.material.card.MaterialCardView
                    android:id="@+id/search_card"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="16dp"
                    app:cardCornerRadius="16dp"
                    app:cardElevation="8dp"
                    app:cardBackgroundColor="@android:color/white"
                    app:strokeWidth="0dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:padding="20dp">

                        <!-- Search Container -->
                        <LinearLayout
                            android:id="@+id/searchContainer"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:background="@drawable/search_background"
                            android:padding="12dp"
                            android:gravity="center_vertical">

                            <ImageView
                                android:layout_width="24dp"
                                android:layout_height="24dp"
                                android:src="@drawable/ic_search"
                                android:layout_marginEnd="12dp"
                                app:tint="@color/search_icon_color" />

                            <androidx.appcompat.widget.SearchView
                                android:id="@+id/search_view"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:background="@android:color/transparent"
                                app:queryHint="Tìm kiếm điều luật..."
                                app:iconifiedByDefault="false"
                                app:searchIcon="@null"
                                app:closeIcon="@drawable/ic_clear" />

                        </LinearLayout>

                    </LinearLayout>

                </com.google.android.material.card.MaterialCardView>

                <!-- Disclaimer Card -->
                <com.google.android.material.card.MaterialCardView
                    android:id="@+id/disclaimerBanner"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="16dp"
                    android:visibility="visible"
                    app:cardCornerRadius="16dp"
                    app:cardElevation="4dp"
                    app:cardBackgroundColor="#FFF3E0"
                    app:strokeColor="#FFB74D"
                    app:strokeWidth="1dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:padding="16dp"
                        android:gravity="center_vertical">

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:src="@drawable/ic_info"
                            android:layout_marginEnd="12dp"
                            app:tint="#FF9800" />

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="@string/disclaimer_title"
                            android:textSize="14sp"
                            android:textColor="#E65100"
                            android:fontFamily="sans-serif-medium" />

                    </LinearLayout>

                </com.google.android.material.card.MaterialCardView>

                <!-- Progress Indicator -->
                <com.google.android.material.progressindicator.LinearProgressIndicator
                    android:id="@+id/progressBar"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="16dp"
                    android:visibility="gone"
                    android:indeterminate="true"
                    app:indicatorColor="?attr/colorPrimary"
                    app:trackCornerRadius="4dp" />

                <!-- Content Card -->
                <com.google.android.material.card.MaterialCardView
                    android:id="@+id/content_card"
                    android:layout_width="match_parent"
                    android:layout_height="0dp"
                    android:layout_weight="1"
                    android:layout_marginBottom="16dp"
                    app:cardCornerRadius="16dp"
                    app:cardElevation="8dp"
                    app:cardBackgroundColor="@android:color/white"
                    app:strokeWidth="0dp">

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/recyclerView"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:padding="16dp"
                        android:clipToPadding="false"
                        android:scrollbars="vertical"
                        android:fadeScrollbars="true"
                        android:background="@android:color/transparent" />

                </com.google.android.material.card.MaterialCardView>

                <!-- Ad Card -->
                <com.google.android.material.card.MaterialCardView
                    android:id="@+id/adContainer"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="16dp"
                    android:visibility="gone"
                    app:cardCornerRadius="16dp"
                    app:cardElevation="4dp"
                    app:cardBackgroundColor="#F8F9FA"
                    app:strokeColor="#E9ECEF"
                    app:strokeWidth="1dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:padding="16dp">

                        <!-- Subtle Ad Label -->
                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/ad_label"
                            android:textSize="10sp"
                            android:textColor="#6C757D"
                            android:layout_gravity="center_horizontal"
                            android:layout_marginBottom="8dp"
                            android:alpha="0.8"
                            android:fontFamily="sans-serif" />

                        <!-- Banner Ad Container -->
                        <LinearLayout
                            android:id="@+id/adViewContainer"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="vertical"
                            android:gravity="center"
                            android:minHeight="50dp" />

                    </LinearLayout>

                </com.google.android.material.card.MaterialCardView>

            </LinearLayout>

        </androidx.core.widget.NestedScrollView>

        <!-- Floating Action Button -->
        <com.google.android.material.floatingactionbutton.FloatingActionButton
            android:id="@+id/fab_search"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_margin="16dp"
            android:src="@drawable/ic_search"
            android:contentDescription="Tìm kiếm nhanh"
            app:layout_anchor="@id/content_card"
            app:layout_anchorGravity="bottom|end"
            app:backgroundTint="?attr/colorPrimary"
            app:tint="@android:color/white"
            app:elevation="8dp" />

        <!-- Bottom App Bar -->
        <com.google.android.material.bottomappbar.BottomAppBar
            android:id="@+id/bottom_app_bar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="bottom"
            app:backgroundTint="@android:color/white"
            app:elevation="8dp"
            app:fabCradleMargin="8dp"
            app:fabCradleRoundedCornerRadius="16dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center"
                android:paddingVertical="8dp">

                <!-- Home Button -->
                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btnHome"
                    style="@style/Widget.Material3.Button.IconButton"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    app:icon="@drawable/ic_home"
                    app:iconTint="?attr/colorPrimary"
                    android:contentDescription="Trang chủ" />

                <!-- Search Button -->
                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btnSearch"
                    style="@style/Widget.Material3.Button.IconButton"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    app:icon="@drawable/ic_search"
                    app:iconTint="?attr/colorPrimary"
                    android:contentDescription="@string/search_button" />

                <!-- Bookmark Button -->
                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btnBookmark"
                    style="@style/Widget.Material3.Button.IconButton"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    app:icon="@drawable/ic_bookmark"
                    app:iconTint="?attr/colorPrimary"
                    android:contentDescription="Bookmark" />

                <!-- Share Button -->
                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btnShare"
                    style="@style/Widget.Material3.Button.IconButton"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    app:icon="@drawable/ic_share"
                    app:iconTint="?attr/colorPrimary"
                    android:contentDescription="Chia sẻ" />

                <!-- Settings Button -->
                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btnSettings"
                    style="@style/Widget.Material3.Button.IconButton"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    app:icon="@drawable/ic_settings"
                    app:iconTint="?attr/colorPrimary"
                    android:contentDescription="Cài đặt" />

            </LinearLayout>

        </com.google.android.material.bottomappbar.BottomAppBar>

    </androidx.coordinatorlayout.widget.CoordinatorLayout>

    <!-- Navigation Drawer -->
    <com.google.android.material.navigation.NavigationView
        android:id="@+id/navigation_view"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:layout_gravity="start"
        android:fitsSystemWindows="true"
        app:headerLayout="@layout/nav_header"
        app:menu="@menu/nav_menu" />

</androidx.drawerlayout.widget.DrawerLayout>

<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/main"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background_light"
    android:fitsSystemWindows="true"
    tools:context=".MainActivity">

    <!-- View chứa Banner AdMob -->
    <LinearLayout
        android:id="@+id/adContainer"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/background_light"
        android:orientation="vertical"
        android:padding="8dp"
        android:gravity="center"
        android:fitsSystemWindows="true"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <!-- Thông báo về quảng cáo -->
        <TextView
            android:id="@+id/adLabel"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/ad_label"
            android:textColor="@color/text_secondary"
            android:textSize="14sp"
            android:padding="4dp"
            android:layout_marginBottom="4dp"
            android:background="#F5F5F5"
            android:fontFamily="sans-serif-medium"/>

        <!-- Quảng cáo AdMob Banner -->
        <com.google.android.gms.ads.AdView
            android:id="@+id/adView"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            app:adSize="BANNER"
            app:adUnitId="@string/admob_banner_id"/>
    </LinearLayout>

    <!-- ProgressBar -->
    <ProgressBar
        android:id="@+id/progressBar"
        style="?android:attr/progressBarStyleHorizontal"
        android:layout_width="match_parent"
        android:layout_height="2dp"
        android:visibility="gone"
        android:layout_marginTop="8dp"
        android:fitsSystemWindows="true"
        app:layout_constraintTop_toBottomOf="@id/adContainer"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"/>

    <!-- WebView hiển thị nội dung luật -->
    <WebView
        android:id="@+id/webView"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:background="@color/background_light"
        android:layout_marginTop="8dp"
        android:fitsSystemWindows="true"
        app:layout_constraintTop_toBottomOf="@id/progressBar"
        app:layout_constraintBottom_toTopOf="@id/bottom_navigation"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"/>

    <!-- Thanh điều hướng dưới cùng -->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/bottom_navigation"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/nav_green"
        android:padding="8dp"
        android:elevation="8dp"
        android:fitsSystemWindows="true"
        android:paddingBottom="8dp"
        app:layout_constraintBottom_toBottomOf="parent">

        <!-- Nút Home -->
        <ImageButton
            android:id="@+id/btnHome"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:background="?attr/selectableItemBackground"
            android:src="@drawable/ic_home"
            android:tint="@color/white"
            android:padding="12dp"
            android:contentDescription="@string/home_button"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"/>

        <!-- Thanh tìm kiếm -->
        <EditText
            android:id="@+id/searchText"
            android:layout_width="0dp"
            android:layout_height="40dp"
            android:hint="@string/search_hint"
            android:textColor="@color/white"
            android:textColorHint="#80FFFFFF"
            android:background="@drawable/search_background"
            android:paddingHorizontal="12dp"
            android:inputType="text"
            android:fontFamily="sans-serif"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toEndOf="@id/btnHome"
            app:layout_constraintEnd_toStartOf="@id/btnSearch"
            app:layout_constraintTop_toTopOf="parent"
            android:layout_marginHorizontal="8dp"/>

        <!-- Nút Tìm kiếm -->
        <ImageButton
            android:id="@+id/btnSearch"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:background="?attr/selectableItemBackground"
            android:src="@drawable/ic_search"
            android:tint="@color/white"
            android:padding="12dp"
            android:contentDescription="@string/search_button"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@id/btnPrivacyPolicy"
            app:layout_constraintTop_toTopOf="parent"/>

        <!-- Nút Privacy Policy -->
        <ImageButton
            android:id="@+id/btnPrivacyPolicy"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:background="?attr/selectableItemBackground"
            android:src="@drawable/ic_privacy"
            android:tint="@color/white"
            android:padding="12dp"
            android:contentDescription="@string/privacy_policy_button"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"/>

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>

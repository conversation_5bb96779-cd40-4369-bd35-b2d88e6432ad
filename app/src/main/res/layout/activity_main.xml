<?xml version="1.0" encoding="utf-8"?>
<androidx.drawerlayout.widget.DrawerLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/drawer_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true"
    tools:context=".MainActivity">

    <!-- Main content -->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/main_content"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="?attr/colorSurface">

        <!-- Top App Bar -->
        <com.google.android.material.appbar.MaterialToolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="?attr/colorPrimary"
            android:elevation="4dp"
            app:title="@string/app_name"
            app:titleTextColor="?attr/colorOnPrimary"
            app:navigationIcon="@drawable/ic_menu"
            app:navigationIconTint="?attr/colorOnPrimary"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent">

            <!-- Custom Search Layout -->
            <LinearLayout
                android:id="@+id/search_container"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="8dp"
                android:layout_marginEnd="8dp"
                android:layout_marginTop="4dp"
                android:layout_marginBottom="4dp"
                android:orientation="horizontal"
                android:background="@drawable/search_view_background"
                android:visibility="gone"
                android:gravity="center_vertical">

                <EditText
                    android:id="@+id/search_edit_text"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:layout_marginStart="16dp"
                    android:layout_marginEnd="8dp"
                    android:background="@android:color/transparent"
                    android:hint="@string/search_hint"
                    android:textColor="@color/search_text"
                    android:textColorHint="@color/search_hint"
                    android:textSize="16sp"
                    android:maxLines="1"
                    android:imeOptions="actionSearch"
                    android:inputType="text" />

                <ImageButton
                    android:id="@+id/search_button"
                    android:layout_width="40dp"
                    android:layout_height="40dp"
                    android:layout_marginEnd="8dp"
                    android:background="?attr/selectableItemBackgroundBorderless"
                    android:src="@drawable/ic_search"
                    android:tint="@color/search_icon"
                    android:contentDescription="Tìm kiếm"
                    android:visibility="gone" />

                <ImageButton
                    android:id="@+id/search_clear"
                    android:layout_width="40dp"
                    android:layout_height="40dp"
                    android:layout_marginEnd="8dp"
                    android:background="?attr/selectableItemBackgroundBorderless"
                    android:src="@drawable/ic_close"
                    android:tint="@color/search_icon"
                    android:contentDescription="Xóa"
                    android:visibility="gone" />

            </LinearLayout>

        </com.google.android.material.appbar.MaterialToolbar>

        <!-- Disclaimer Banner -->
        <com.google.android.material.card.MaterialCardView
            android:id="@+id/disclaimerBanner"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="8dp"
            app:cardElevation="2dp"
            app:cardCornerRadius="8dp"
            app:cardBackgroundColor="#FFF3E0"
            app:layout_constraintTop_toBottomOf="@id/toolbar"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="8dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@string/disclaimer_title"
                    android:textStyle="bold"
                    android:textColor="#E65100"
                    android:textSize="14sp"
                    android:layout_marginBottom="4dp" />

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@string/disclaimer"
                    android:textColor="#333333"
                    android:textSize="12sp" />

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@string/source_info"
                    android:textColor="#333333"
                    android:textSize="12sp"
                    android:layout_marginTop="4dp" />

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>



        <!-- Progress Indicator -->
        <com.google.android.material.progressindicator.LinearProgressIndicator
            android:id="@+id/progressBar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:visibility="gone"
            android:indeterminate="true"
            app:indicatorColor="?attr/colorPrimary"
            app:layout_constraintTop_toBottomOf="@id/disclaimerBanner"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"/>

        <!-- RecyclerView hiển thị nội dung luật -->
        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recyclerView"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:background="?attr/colorSurface"
            android:layout_margin="8dp"
            android:scrollbars="vertical"
            android:fadeScrollbars="true"
            app:layout_constraintTop_toBottomOf="@id/progressBar"
            app:layout_constraintBottom_toTopOf="@id/adContainer"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"/>

        <!-- Banner AdMob - User-friendly design at bottom -->
        <com.google.android.material.card.MaterialCardView
            android:id="@+id/adContainer"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="4dp"
            android:layout_marginEnd="4dp"
            android:layout_marginBottom="2dp"
            android:visibility="gone"
            app:cardElevation="1dp"
            app:cardCornerRadius="12dp"
            app:cardBackgroundColor="?attr/colorSurfaceVariant"
            app:strokeWidth="0dp"
            app:layout_constraintBottom_toTopOf="@id/bottom_app_bar"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="6dp">

                <!-- Subtle Ad Label -->
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/ad_label"
                    android:textSize="9sp"
                    android:textColor="?attr/colorOnSurfaceVariant"
                    android:layout_gravity="center_horizontal"
                    android:layout_marginBottom="2dp"
                    android:alpha="0.7"
                    android:fontFamily="sans-serif"/>

                <!-- Banner Ad Container -->
                <LinearLayout
                    android:id="@+id/adViewContainer"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:gravity="center"
                    android:minHeight="50dp" />

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- Bottom App Bar -->
        <com.google.android.material.bottomappbar.BottomAppBar
            android:id="@+id/bottom_app_bar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="bottom"
            app:backgroundTint="?attr/colorSurface"
            app:elevation="8dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center"
                android:paddingVertical="8dp">

                <!-- Home Button -->
                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btnHome"
                    style="@style/Widget.Material3.Button.IconButton"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    app:icon="@drawable/ic_home"
                    app:iconTint="?attr/colorOnSurface"
                    android:contentDescription="@string/home_button"/>

                <!-- Search Button -->
                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btnSearch"
                    style="@style/Widget.Material3.Button.IconButton"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    app:icon="@drawable/ic_search"
                    app:iconTint="?attr/colorOnSurface"
                    android:contentDescription="@string/search_button"/>

                <!-- Bookmark Button -->
                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btnBookmark"
                    style="@style/Widget.Material3.Button.IconButton"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    app:icon="@drawable/ic_bookmark"
                    app:iconTint="?attr/colorOnSurface"
                    android:contentDescription="Bookmark"/>

                <!-- Share Button -->
                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btnShare"
                    style="@style/Widget.Material3.Button.IconButton"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    app:icon="@drawable/ic_share"
                    app:iconTint="?attr/colorOnSurface"
                    android:contentDescription="Chia sẻ"/>

                <!-- Settings Button -->
                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btnSettings"
                    style="@style/Widget.Material3.Button.IconButton"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    app:icon="@drawable/ic_settings"
                    app:iconTint="?attr/colorOnSurface"
                    android:contentDescription="Cài đặt"/>

            </LinearLayout>

        </com.google.android.material.bottomappbar.BottomAppBar>

    </androidx.constraintlayout.widget.ConstraintLayout>

    <!-- Navigation Drawer -->
    <com.google.android.material.navigation.NavigationView
        android:id="@+id/navigation_view"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:layout_gravity="start"
        android:fitsSystemWindows="true"
        app:headerLayout="@layout/nav_header"
        app:menu="@menu/nav_menu" />

</androidx.drawerlayout.widget.DrawerLayout>

<?xml version="1.0" encoding="utf-8"?>
<androidx.drawerlayout.widget.DrawerLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/drawer_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true"
    tools:context=".MainActivity">

    <!-- Main content -->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/main_content"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="?attr/colorSurface">

        <!-- Top App Bar -->
        <com.google.android.material.appbar.MaterialToolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="?attr/colorPrimary"
            android:elevation="4dp"
            app:title="@string/app_name"
            app:titleTextColor="?attr/colorOnPrimary"
            app:navigationIcon="@drawable/ic_menu"
            app:navigationIconTint="?attr/colorOnPrimary"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent">

            <!-- Custom Search Layout -->
            <LinearLayout
                android:id="@+id/search_container"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="8dp"
                android:layout_marginEnd="8dp"
                android:layout_marginTop="4dp"
                android:layout_marginBottom="4dp"
                android:orientation="horizontal"
                android:background="@drawable/search_view_background"
                android:visibility="gone"
                android:gravity="center_vertical">

                <EditText
                    android:id="@+id/search_edit_text"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:layout_marginStart="16dp"
                    android:layout_marginEnd="8dp"
                    android:background="@android:color/transparent"
                    android:hint="@string/search_hint"
                    android:textColor="@color/search_text"
                    android:textColorHint="@color/search_hint"
                    android:textSize="16sp"
                    android:maxLines="1"
                    android:imeOptions="actionSearch"
                    android:inputType="text" />

                <ImageButton
                    android:id="@+id/search_button"
                    android:layout_width="40dp"
                    android:layout_height="40dp"
                    android:layout_marginEnd="8dp"
                    android:background="?attr/selectableItemBackgroundBorderless"
                    android:src="@drawable/ic_search"
                    android:tint="@color/search_icon"
                    android:contentDescription="Tìm kiếm"
                    android:visibility="gone" />

                <ImageButton
                    android:id="@+id/search_clear"
                    android:layout_width="40dp"
                    android:layout_height="40dp"
                    android:layout_marginEnd="8dp"
                    android:background="?attr/selectableItemBackgroundBorderless"
                    android:src="@drawable/ic_close"
                    android:tint="@color/search_icon"
                    android:contentDescription="Xóa"
                    android:visibility="gone" />

            </LinearLayout>

        </com.google.android.material.appbar.MaterialToolbar>

        <!-- View chứa Banner AdMob -->
        <com.google.android.material.card.MaterialCardView
            android:id="@+id/adContainer"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="8dp"
            android:visibility="gone"
            app:cardElevation="2dp"
            app:cardCornerRadius="8dp"
            app:layout_constraintTop_toBottomOf="@id/toolbar"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="8dp"
                android:gravity="center">

                <!-- Thông báo về quảng cáo -->
                <TextView
                    android:id="@+id/adLabel"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/ad_label"
                    android:textColor="?attr/colorOnSurfaceVariant"
                    android:textSize="12sp"
                    android:layout_marginBottom="4dp"
                    android:fontFamily="sans-serif-medium"/>

                <!-- Quảng cáo AdMob Banner -->
                <com.google.android.gms.ads.AdView
                    android:id="@+id/adView"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    app:adSize="BANNER"
                    app:adUnitId="@string/admob_banner_id"/>

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- Progress Indicator -->
        <com.google.android.material.progressindicator.LinearProgressIndicator
            android:id="@+id/progressBar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:visibility="gone"
            android:indeterminate="true"
            app:indicatorColor="?attr/colorPrimary"
            app:layout_constraintTop_toBottomOf="@id/adContainer"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"/>

        <!-- RecyclerView hiển thị nội dung luật -->
        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recyclerView"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:background="?attr/colorSurface"
            android:layout_margin="8dp"
            android:scrollbars="vertical"
            android:fadeScrollbars="true"
            app:layout_constraintTop_toBottomOf="@id/progressBar"
            app:layout_constraintBottom_toTopOf="@id/bottom_app_bar"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"/>

        <!-- Bottom App Bar -->
        <com.google.android.material.bottomappbar.BottomAppBar
            android:id="@+id/bottom_app_bar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="bottom"
            app:backgroundTint="?attr/colorSurface"
            app:elevation="8dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center"
                android:paddingVertical="8dp">

                <!-- Home Button -->
                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btnHome"
                    style="@style/Widget.Material3.Button.IconButton"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    app:icon="@drawable/ic_home"
                    app:iconTint="?attr/colorOnSurface"
                    android:contentDescription="@string/home_button"/>

                <!-- Search Button -->
                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btnSearch"
                    style="@style/Widget.Material3.Button.IconButton"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    app:icon="@drawable/ic_search"
                    app:iconTint="?attr/colorOnSurface"
                    android:contentDescription="@string/search_button"/>

                <!-- Bookmark Button -->
                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btnBookmark"
                    style="@style/Widget.Material3.Button.IconButton"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    app:icon="@drawable/ic_bookmark"
                    app:iconTint="?attr/colorOnSurface"
                    android:contentDescription="Bookmark"/>

                <!-- Share Button -->
                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btnShare"
                    style="@style/Widget.Material3.Button.IconButton"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    app:icon="@drawable/ic_share"
                    app:iconTint="?attr/colorOnSurface"
                    android:contentDescription="Chia sẻ"/>

                <!-- Settings Button -->
                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btnSettings"
                    style="@style/Widget.Material3.Button.IconButton"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    app:icon="@drawable/ic_settings"
                    app:iconTint="?attr/colorOnSurface"
                    android:contentDescription="Cài đặt"/>

            </LinearLayout>

        </com.google.android.material.bottomappbar.BottomAppBar>

    </androidx.constraintlayout.widget.ConstraintLayout>

    <!-- Navigation Drawer -->
    <com.google.android.material.navigation.NavigationView
        android:id="@+id/navigation_view"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:layout_gravity="start"
        android:fitsSystemWindows="true"
        app:headerLayout="@layout/nav_header"
        app:menu="@menu/nav_menu" />

</androidx.drawerlayout.widget.DrawerLayout>

<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/card_view"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="8dp"
    app:cardCornerRadius="8dp"
    app:cardElevation="2dp"
    app:strokeWidth="1dp"
    app:strokeColor="?attr/colorOutlineVariant"
    android:clickable="true"
    android:focusable="true"
    android:foreground="?android:attr/selectableItemBackground">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- Header with location and actions -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical">

            <TextView
                android:id="@+id/location_text"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:textAppearance="@style/TextAppearance.Material3.LabelMedium"
                android:textColor="?attr/colorPrimary"
                android:textStyle="bold"
                android:text="Chương I, Điều 1" />

            <ImageButton
                android:id="@+id/edit_button"
                style="@style/Widget.Material3.Button.IconButton"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@drawable/ic_edit"
                android:contentDescription="Chỉnh sửa"
                app:tint="?attr/colorOnSurfaceVariant" />

            <ImageButton
                android:id="@+id/delete_button"
                style="@style/Widget.Material3.Button.IconButton"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@drawable/ic_delete"
                android:contentDescription="Xóa"
                app:tint="?attr/colorError" />

        </LinearLayout>

        <!-- Title -->
        <TextView
            android:id="@+id/title_text"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:textAppearance="@style/TextAppearance.Material3.TitleSmall"
            android:textColor="?attr/colorOnSurface"
            android:textStyle="bold"
            android:text="Phạm vi điều chỉnh"
            android:lineSpacingExtra="2dp" />

        <!-- Content Preview -->
        <TextView
            android:id="@+id/content_text"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:textAppearance="@style/TextAppearance.Material3.BodyMedium"
            android:textColor="?attr/colorOnSurfaceVariant"
            android:text="Bộ luật này quy định địa vị pháp lý, chuẩn mực pháp lý về cách ứng xử của cá nhân, pháp nhân..."
            android:lineSpacingExtra="4dp"
            android:maxLines="3"
            android:ellipsize="end" />

        <!-- Note -->
        <TextView
            android:id="@+id/note_text"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:textAppearance="@style/TextAppearance.Material3.BodySmall"
            android:textColor="?attr/colorSecondary"
            android:textStyle="italic"
            android:text="Ghi chú của tôi..."
            android:lineSpacingExtra="2dp"
            android:visibility="gone" />

        <!-- Date -->
        <TextView
            android:id="@+id/date_text"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:textAppearance="@style/TextAppearance.Material3.LabelSmall"
            android:textColor="?attr/colorOnSurfaceVariant"
            android:text="25/12/2024 14:30"
            android:gravity="end" />

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>

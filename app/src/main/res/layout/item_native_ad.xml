<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="8dp"
    app:cardCornerRadius="8dp"
    app:cardElevation="2dp"
    app:strokeWidth="1dp"
    app:strokeColor="?attr/colorOutline">

    <com.google.android.gms.ads.nativead.NativeAdView
        android:id="@+id/native_ad_view"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="12dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <!-- Ad <PERSON> -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:layout_marginBottom="8dp">

                <ImageView
                    android:id="@+id/ad_app_icon"
                    android:layout_width="40dp"
                    android:layout_height="40dp"
                    android:layout_marginEnd="12dp"
                    android:scaleType="centerCrop"
                    android:background="@drawable/ic_launcher_background" />

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="vertical">

                    <TextView
                        android:id="@+id/ad_headline"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:textStyle="bold"
                        android:textSize="16sp"
                        android:textColor="?attr/colorOnSurface"
                        android:maxLines="1"
                        android:ellipsize="end" />

                    <TextView
                        android:id="@+id/ad_advertiser"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:textSize="12sp"
                        android:textColor="?attr/colorOnSurfaceVariant"
                        android:maxLines="1"
                        android:ellipsize="end" />

                </LinearLayout>

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Quảng cáo"
                    android:textSize="10sp"
                    android:textColor="?attr/colorOnSurfaceVariant"
                    android:background="@drawable/ad_label_background"
                    android:padding="4dp" />

            </LinearLayout>

            <!-- Ad Body -->
            <TextView
                android:id="@+id/ad_body"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:textSize="14sp"
                android:textColor="?attr/colorOnSurface"
                android:layout_marginBottom="8dp"
                android:maxLines="3"
                android:ellipsize="end" />

            <!-- Media View -->
            <com.google.android.gms.ads.nativead.MediaView
                android:id="@+id/ad_media"
                android:layout_width="match_parent"
                android:layout_height="200dp"
                android:layout_marginBottom="8dp"
                android:visibility="gone" />

            <!-- Call to Action -->
            <com.google.android.material.button.MaterialButton
                android:id="@+id/ad_call_to_action"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="end"
                android:textSize="12sp"
                style="@style/Widget.Material3.Button.UnelevatedButton" />

        </LinearLayout>

    </com.google.android.gms.ads.nativead.NativeAdView>

</com.google.android.material.card.MaterialCardView>
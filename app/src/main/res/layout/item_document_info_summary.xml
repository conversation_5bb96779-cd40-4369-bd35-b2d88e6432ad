<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/card_view"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="8dp"
    app:cardCornerRadius="12dp"
    app:cardElevation="4dp"
    app:strokeWidth="1dp"
    app:strokeColor="?attr/colorPrimary">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <TextView
            android:id="@+id/title_text"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Tóm tắt nội dung"
            android:textSize="18sp"
            android:textStyle="bold"
            android:textColor="?attr/colorPrimary"
            android:layout_marginBottom="8dp"
            android:drawableStart="@drawable/ic_description"
            android:drawablePadding="8dp"
            android:gravity="center_vertical" />

        <TextView
            android:id="@+id/content_text"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Summary content"
            android:textSize="16sp"
            android:textColor="?attr/colorOnSurface"
            android:lineSpacingExtra="4dp"
            android:justificationMode="inter_word" />

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>

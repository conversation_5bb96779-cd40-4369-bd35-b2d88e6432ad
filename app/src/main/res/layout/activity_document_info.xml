<?xml version="1.0" encoding="utf-8"?>
<androidx.drawerlayout.widget.DrawerLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/drawer_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true"
    tools:openDrawer="start">

    <!-- Main content -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <!-- Toolbar -->
        <com.google.android.material.appbar.MaterialToolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="?attr/colorPrimary"
            android:elevation="4dp"
            app:title="Thông tin văn bản"
            app:titleTextColor="@android:color/white"
            app:navigationIcon="@drawable/ic_menu" />

        <!-- Progress Bar -->
        <ProgressBar
            android:id="@+id/progressBar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:indeterminate="true"
            android:visibility="visible"
            style="?android:attr/progressBarStyleHorizontal" />

        <!-- Main Content Area -->
        <ScrollView
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:fillViewport="true"
            android:padding="16dp">

            <LinearLayout
                android:id="@+id/main_content_container"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <!-- Document Information Card -->
                <com.google.android.material.card.MaterialCardView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="16dp"
                    app:cardCornerRadius="16dp"
                    app:cardElevation="4dp"
                    app:strokeColor="?attr/colorPrimary"
                    app:strokeWidth="1">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:padding="24dp">

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="📄 Thông tin văn bản"
                            android:textSize="18sp"
                            android:textStyle="bold"
                            android:textColor="?attr/colorPrimary"
                            android:layout_marginBottom="16dp" />

                        <TextView
                            android:id="@+id/document_name"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="Bộ Luật Dân sự Việt Nam"
                            android:textSize="16sp"
                            android:textStyle="bold"
                            android:layout_marginBottom="8dp" />

                        <TextView
                            android:id="@+id/document_summary"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="Bộ luật quy định về các quan hệ dân sự..."
                            android:textSize="14sp"
                            android:textColor="@android:color/darker_gray" />

                    </LinearLayout>

                </com.google.android.material.card.MaterialCardView>

                <!-- Statistics Card -->
                <com.google.android.material.card.MaterialCardView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="16dp"
                    app:cardCornerRadius="16dp"
                    app:cardElevation="4dp"
                    app:strokeColor="?attr/colorPrimary"
                    app:strokeWidth="1">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:padding="24dp">

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="📊 Thống kê nội dung"
                            android:textSize="18sp"
                            android:textStyle="bold"
                            android:textColor="?attr/colorPrimary"
                            android:layout_marginBottom="16dp" />

                        <TextView
                            android:id="@+id/chapter_count"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="Số chương: 24"
                            android:textSize="14sp"
                            android:layout_marginBottom="8dp" />

                        <TextView
                            android:id="@+id/article_count"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="Số điều: 689"
                            android:textSize="14sp" />

                    </LinearLayout>

                </com.google.android.material.card.MaterialCardView>

                <!-- Metadata Card -->
                <com.google.android.material.card.MaterialCardView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:cardCornerRadius="16dp"
                    app:cardElevation="4dp"
                    app:strokeColor="?attr/colorPrimary"
                    app:strokeWidth="1">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:padding="24dp">

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="ℹ️ Thông tin ban hành"
                            android:textSize="18sp"
                            android:textStyle="bold"
                            android:textColor="?attr/colorPrimary"
                            android:layout_marginBottom="16dp" />

                        <TextView
                            android:id="@+id/issuing_authority"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="Cơ quan ban hành: Quốc hội Việt Nam"
                            android:textSize="14sp"
                            android:layout_marginBottom="8dp" />

                        <TextView
                            android:id="@+id/issue_date"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="Ngày ban hành: 24/11/2015"
                            android:textSize="14sp"
                            android:layout_marginBottom="8dp" />

                        <TextView
                            android:id="@+id/effective_date"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="Ngày hiệu lực: 01/01/2017"
                            android:textSize="14sp" />

                    </LinearLayout>

                </com.google.android.material.card.MaterialCardView>

            </LinearLayout>

        </ScrollView>

        <!-- AdMob Banner -->
        <com.google.android.gms.ads.AdView
            android:id="@+id/adView"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal" />

    </LinearLayout>

    <!-- Navigation Drawer -->
    <com.google.android.material.navigation.NavigationView
        android:id="@+id/nav_view"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:layout_gravity="start"
        android:fitsSystemWindows="true"
        app:headerLayout="@layout/nav_header"
        app:menu="@menu/activity_main_drawer" />

</androidx.drawerlayout.widget.DrawerLayout>

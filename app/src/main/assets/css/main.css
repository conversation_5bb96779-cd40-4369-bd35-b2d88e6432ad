/* Modern CSS for <PERSON><PERSON> Dân Sự */
:root {
    --primary-color: #006C4C;
    --primary-light: #89F8C7;
    --secondary-color: #4D6357;
    --background-color: #F6FDF9;
    --surface-color: #FFFFFF;
    --text-primary: #181D1A;
    --text-secondary: #404943;
    --border-color: #DCE5DD;
    --shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

body, html {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
    margin: 0;
    padding: 16px;
    background-color: var(--background-color);
    color: var(--text-primary);
    line-height: 1.6;
    font-size: 16px;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    color: var(--primary-color);
    font-weight: 600;
    margin: 24px 0 16px 0;
    line-height: 1.3;
}

h1 { font-size: 28px; }
h2 { font-size: 24px; }
h3 { font-size: 20px; }
h4 { font-size: 18px; }

p {
    font-size: 16px;
    line-height: 1.6;
    margin: 0 0 16px 0;
    color: var(--text-primary);
}

/* Links */
a {
    color: var(--primary-color);
    text-decoration: none;
    border-bottom: 1px solid transparent;
    transition: all 0.2s ease;
}

a:hover {
    border-bottom-color: var(--primary-color);
    background-color: var(--primary-light);
    padding: 2px 4px;
    border-radius: 4px;
}


        /*Floating Back-To-Top Button*/
          #myBtn {
          position: fixed;
          bottom: 10px;
          float: right;
          right: 18.5%;
          left: 77.25%;
          max-width: 30px;
          width: 100%;
          font-size: 12px;
          border-color: rgba(85, 85, 85, 0.2);
          background-color: rgb(38, 128, 231, 1);
          padding: 5px;
          border-radius: 4px;

            }
        /*On Hover Color Change*/
            #myBtn:hover {
                background-color: #7dbbf1;
            }

/* Modern Table Styling */
.styled-table {
    border-collapse: collapse;
    margin: 24px 0;
    font-size: 16px;
    font-family: inherit;
    width: 100%;
    background-color: var(--surface-color);
    border-radius: 12px;
    overflow: hidden;
    box-shadow: var(--shadow);
}

.styled-table thead tr {
    background-color: var(--primary-color);
    color: white;
    text-align: left;
    font-weight: 600;
}

.styled-table th,
.styled-table td {
    padding: 16px 20px;
    border-bottom: 1px solid var(--border-color);
}

.styled-table tbody tr {
    transition: background-color 0.2s ease;
}

.styled-table tbody tr:nth-of-type(even) {
    background-color: #f8fffe;
}

.styled-table tbody tr:hover {
    background-color: var(--primary-light);
}

.styled-table tbody tr:last-of-type td {
    border-bottom: none;
}

.styled-table tbody tr.active-row {
    font-weight: 600;
    color: var(--primary-color);
    background-color: var(--primary-light);
}

       /*Footnote for law*/
        a.footnote {
       text-decoration:none;
       background-color: #FEF6BB;
       padding-left: 2px;
       padding-right: 2px;
       margin-right: 2px;
       -webkit-transition: all 2s ease;
       -moz-transition: all 2s ease;
       -o-transition: all 2s ease;
       transition: all 2s ease;
    }

    a.footnote span {
       z-index: -1;
       opacity: 0;
       position: fixed;
       left: 15px;
       bottom: 50px;
       margin-left: 0px;
       margin-right: 18px;
       padding:14px 20px;
       border-radius:4px; box-shadow: 5px 5px 8px #CCC;
       border:1px solid #DCA;
       background-color: #FEF6BB;
       -webkit-transition: all 2s ease;
       -moz-transition: all 2s ease;
       -o-transition: all 2s ease;
       transition: all 2s ease;
    }

    a.footnote:hover span {
       z-index: 9;
       opacity: 1;
       -webkit-transition: all 2s ease;
       -moz-transition: all 2s ease;
       -o-transition: all 2s ease;
       transition: all 2s ease;
    }

    /* CSS TAB_Style the buttons that are used to open and close the accordion panel */
       .accordion {
         background-color: #eee;
         color: #12af83;
         cursor: pointer;
         padding: 18px;
         width: 100%;
         border: none;
         text-align: left;
         outline: none;
         font-size: 18px;
         transition: 0.4s;
       }

       .active, .accordion:hover {
         background-color: #ccc;
       }

       .accordion:after {
         content: '\002B';
         color: #777;
         font-weight: bold;
         float: right;
         margin-left: 5px;
       }

       .active:after {
         content: "\2212";
       }

       .panel {
         padding: 0 18px;
         background-color: white;
         max-height: 0;
         overflow: hidden;
         transition: max-height 0.2s ease-out;
       }


       /*Note CSS*/
       div {
         margin-bottom: 15px;
         padding: 4px 12px;
       }

       .danger {
         background-color: #ffdddd;
         border-left: 6px solid #f44336;
       }

       .success {
         background-color: #ddffdd;
         border-left: 6px solid #04AA6D;
       }

       .info {
         background-color: #e7f3fe;
         border-left: 6px solid #2196F3;
       }


       .warning {
         background-color: #ffffcc;
         border-left: 6px solid #ffeb3b;
       }

/* Article and Section Styling */
.MsoNormal {
    margin: 16px 0;
    padding: 12px;
    background-color: var(--surface-color);
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    transition: all 0.2s ease;
}

.MsoNormal:hover {
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
    transform: translateY(-1px);
}

/* Article titles */
.MsoNormal b {
    color: var(--primary-color);
    font-size: 18px;
    display: block;
    margin-bottom: 8px;
    padding-bottom: 8px;
    border-bottom: 2px solid var(--border-color);
}

/* Responsive design */
@media (max-width: 768px) {
    body {
        padding: 12px;
        font-size: 14px;
    }

    h1 { font-size: 24px; }
    h2 { font-size: 20px; }
    h3 { font-size: 18px; }

    .styled-table th,
    .styled-table td {
        padding: 12px 16px;
    }

    .MsoNormal {
        padding: 8px;
        margin: 12px 0;
    }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    :root {
        --background-color: #0F1511;
        --surface-color: #1E1E1E;
        --text-primary: #E0E3DF;
        --text-secondary: #C0C9C1;
        --border-color: #404943;
    }

    .styled-table tbody tr:nth-of-type(even) {
        background-color: #2A2A2A;
    }
}
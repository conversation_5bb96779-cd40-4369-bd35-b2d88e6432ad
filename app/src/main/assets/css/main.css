body, html {
              font-family: "MyVariableFont", "Arial", sans-serif;
        }
        body {
          margin: 20px;
          font-size: 20px;
        }
        h3 {
          font-size: 34px;
          line-height: 40px;
          font-weight: 400;
          color: #424242;
        }
        p {
          font-size: 18px;
          line-height: 20px;
          letter-spacing: 0;
          margin: 0 0 16px;
          color: #424242;
        }


        /*Floating Back-To-Top Button*/
          #myBtn {
          position: fixed;
          bottom: 10px;
          float: right;
          right: 18.5%;
          left: 77.25%;
          max-width: 30px;
          width: 100%;
          font-size: 12px;
          border-color: rgba(85, 85, 85, 0.2);
          background-color: rgb(38, 128, 231, 1);
          padding: 5px;
          border-radius: 4px;

            }
        /*On Hover Color Change*/
            #myBtn:hover {
                background-color: #7dbbf1;
            }

                  .styled-table {
            border-collapse: collapse;
            margin: 25px 0;
            font-size: 0.9em;
            font-family: sans-serif;
            min-width: 100%;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.15);
        }
      .styled-table thead tr {
            background-color: #009879;
            color: #ffffff;
            text-align: left;
        }
        .styled-table th,
        .styled-table td {
            padding: 12px 15px;
        }

        .styled-table tbody tr {
        border-bottom: 1px solid #dddddd;
        }

        .styled-table tbody tr:nth-of-type(even) {
            background-color: #f3f3f3;
        }

        .styled-table tbody tr:last-of-type {
            border-bottom: 2px solid #009879;
        }

        .styled-table tbody tr.active-row {
            font-weight: bold;
            color: #009879;
        }

       /*Footnote for law*/
        a.footnote {
       text-decoration:none;
       background-color: #FEF6BB;
       padding-left: 2px;
       padding-right: 2px;
       margin-right: 2px;
       -webkit-transition: all 2s ease;
       -moz-transition: all 2s ease;
       -o-transition: all 2s ease;
       transition: all 2s ease;
    }

    a.footnote span {
       z-index: -1;
       opacity: 0;
       position: fixed;
       left: 15px;
       bottom: 50px;
       margin-left: 0px;
       margin-right: 18px;
       padding:14px 20px;
       border-radius:4px; box-shadow: 5px 5px 8px #CCC;
       border:1px solid #DCA;
       background-color: #FEF6BB;
       -webkit-transition: all 2s ease;
       -moz-transition: all 2s ease;
       -o-transition: all 2s ease;
       transition: all 2s ease;
    }

    a.footnote:hover span {
       z-index: 9;
       opacity: 1;
       -webkit-transition: all 2s ease;
       -moz-transition: all 2s ease;
       -o-transition: all 2s ease;
       transition: all 2s ease;
    }

    /* CSS TAB_Style the buttons that are used to open and close the accordion panel */
       .accordion {
         background-color: #eee;
         color: #12af83;
         cursor: pointer;
         padding: 18px;
         width: 100%;
         border: none;
         text-align: left;
         outline: none;
         font-size: 18px;
         transition: 0.4s;
       }

       .active, .accordion:hover {
         background-color: #ccc;
       }

       .accordion:after {
         content: '\002B';
         color: #777;
         font-weight: bold;
         float: right;
         margin-left: 5px;
       }

       .active:after {
         content: "\2212";
       }

       .panel {
         padding: 0 18px;
         background-color: white;
         max-height: 0;
         overflow: hidden;
         transition: max-height 0.2s ease-out;
       }
       

       /*Note CSS*/
       div {
         margin-bottom: 15px;
         padding: 4px 12px;
       }

       .danger {
         background-color: #ffdddd;
         border-left: 6px solid #f44336;
       }

       .success {
         background-color: #ddffdd;
         border-left: 6px solid #04AA6D;
       }

       .info {
         background-color: #e7f3fe;
         border-left: 6px solid #2196F3;
       }


       .warning {
         background-color: #ffffcc;
         border-left: 6px solid #ffeb3b;
       }
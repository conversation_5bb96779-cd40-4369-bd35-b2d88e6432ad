/* Reset CSS */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    -webkit-tap-highlight-color: transparent;
}

/* Base styles */
:root {
    --text-primary: #1d1d1f;
    --text-secondary: #86868b;
    --background-primary: #ffffff;
    --background-secondary: #f5f5f7;
    --accent-color: #007AFF;
    --accent-color-dark: #0A84FF;
    --border-radius: 12px;
    --spacing-xs: 4px;
    --spacing-sm: 8px;
    --spacing-md: 16px;
    --spacing-lg: 24px;
    --spacing-xl: 32px;
}

body {
    font-family: -apple-system, system-ui, "SF Pro Text", "Helvetica Neue", "Segoe UI", Roboto, sans-serif;
    line-height: 1.5;
    color: var(--text-primary);
    padding: var(--spacing-md);
    margin: 0;
    background-color: var(--background-primary);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Typography */
h1, h2, h3, h4 {
    font-family: -apple-system, system-ui, "SF Pro Display", "Helvetica Neue", "Segoe UI", Roboto, sans-serif;
    font-weight: 600;
    letter-spacing: -0.022em;
    color: var(--text-primary);
}

h1 {
    font-size: 34px;
    line-height: 1.2;
    margin-bottom: var(--spacing-lg);
    text-align: center;
}

h2 {
    font-size: 28px;
    line-height: 1.3;
    margin-top: var(--spacing-xl);
    margin-bottom: var(--spacing-md);
    color: var(--accent-color);
}

h3 {
    font-size: 22px;
    line-height: 1.3;
    margin-top: var(--spacing-lg);
    margin-bottom: var(--spacing-md);
    color: var(--accent-color);
}

h4 {
    font-size: 20px;
    line-height: 1.4;
    margin-top: var(--spacing-lg);
    margin-bottom: var(--spacing-sm);
}

p {
    font-size: 17px;
    line-height: 1.6;
    margin-bottom: var(--spacing-md);
    letter-spacing: -0.022em;
}

/* Lists */
ul, ol {
    padding-left: var(--spacing-lg);
    margin-bottom: var(--spacing-md);
}

li {
    font-size: 17px;
    line-height: 1.6;
    margin-bottom: var(--spacing-sm);
    letter-spacing: -0.022em;
}

/* Article styles */
.article {
    background-color: var(--background-secondary);
    border-radius: var(--border-radius);
    padding: var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.article:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.article-title {
    font-size: 20px;
    font-weight: 600;
    margin-bottom: var(--spacing-sm);
    color: var(--text-primary);
    letter-spacing: -0.022em;
}

.article-content {
    font-size: 17px;
    line-height: 1.6;
    color: var(--text-primary);
}

/* Section styles */
.chapter {
    margin-bottom: var(--spacing-xl);
}

.section {
    margin-bottom: var(--spacing-lg);
}

/* Utility classes */
.highlight {
    font-weight: 600;
    color: var(--accent-color);
}

.note {
    font-style: italic;
    color: var(--text-secondary);
    font-size: 15px;
    margin-top: var(--spacing-sm);
}

.search-highlight {
    background-color: #fff2cc;
    padding: 2px 4px;
    border-radius: 4px;
}

/* Dark mode */
@media (prefers-color-scheme: dark) {
    :root {
        --text-primary: #f5f5f7;
        --text-secondary: #98989d;
        --background-primary: #000000;
        --background-secondary: #1c1c1e;
        --accent-color: var(--accent-color-dark);
    }

    .article {
        box-shadow: none;
    }

    .article:hover {
        box-shadow: 0 2px 8px rgba(255, 255, 255, 0.05);
    }

    .search-highlight {
        background-color: #634902;
    }
}

/* Responsive design */
@media (max-width: 768px) {
    :root {
        --spacing-md: 12px;
        --spacing-lg: 20px;
        --spacing-xl: 28px;
    }

    body {
        padding: var(--spacing-sm);
    }

    h1 {
        font-size: 28px;
    }

    h2 {
        font-size: 24px;
    }

    h3 {
        font-size: 20px;
    }

    p, li {
        font-size: 16px;
    }

    .article {
        padding: var(--spacing-md);
    }
}

/* Vietnamese text optimization */
body {
    text-rendering: optimizeLegibility;
    font-feature-settings: "kern" 1, "liga" 1;
}

/* Diacritical marks optimization */
:lang(vi) {
    font-feature-settings: "kern" 1, "liga" 1, "dlig" 1;
} 
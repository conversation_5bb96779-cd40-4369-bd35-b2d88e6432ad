package com.oriondev.luatdansu.database.dao;

import androidx.lifecycle.LiveData;
import androidx.room.Dao;
import androidx.room.Insert;
import androidx.room.OnConflictStrategy;
import androidx.room.Query;
import androidx.room.Update;
import androidx.room.Delete;

import com.oriondev.luatdansu.database.entity.SearchHistoryEntity;

import java.util.List;

@Dao
public interface SearchHistoryDao {

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    long insert(SearchHistoryEntity searchHistory);

    @Update
    void update(SearchHistoryEntity searchHistory);

    @Delete
    void delete(SearchHistoryEntity searchHistory);

    @Query("DELETE FROM search_history")
    void deleteAll();

    // Lấy tất cả lịch sử tìm kiếm theo thời gian (mới nhất trước)
    @Query("SELECT * FROM search_history ORDER BY last_accessed DESC")
    LiveData<List<SearchHistoryEntity>> getAllSearchHistory();

    @Query("SELECT * FROM search_history ORDER BY last_accessed DESC")
    List<SearchHistoryEntity> getAllSearchHistorySync();

    // Lấy lịch sử tìm kiếm gần đây (limit)
    @Query("SELECT * FROM search_history ORDER BY last_accessed DESC LIMIT :limit")
    LiveData<List<SearchHistoryEntity>> getRecentSearchHistory(int limit);

    @Query("SELECT * FROM search_history ORDER BY last_accessed DESC LIMIT :limit")
    List<SearchHistoryEntity> getRecentSearchHistorySync(int limit);

    // Tìm kiếm theo query
    @Query("SELECT * FROM search_history WHERE query = :query LIMIT 1")
    SearchHistoryEntity getSearchHistoryByQuery(String query);

    @Query("SELECT * FROM search_history WHERE query = :query LIMIT 1")
    LiveData<SearchHistoryEntity> getSearchHistoryByQueryLive(String query);

    // Tìm kiếm tương tự
    @Query("SELECT * FROM search_history WHERE query LIKE '%' || :query || '%' ORDER BY last_accessed DESC")
    LiveData<List<SearchHistoryEntity>> searchSimilarQueries(String query);

    @Query("SELECT * FROM search_history WHERE query LIKE '%' || :query || '%' ORDER BY last_accessed DESC")
    List<SearchHistoryEntity> searchSimilarQueriesSync(String query);

    // Lấy các query phổ biến (được tìm nhiều lần)
    @Query("SELECT * FROM search_history ORDER BY (last_accessed - searched_at) DESC, last_accessed DESC LIMIT :limit")
    LiveData<List<SearchHistoryEntity>> getPopularQueries(int limit);

    // Cập nhật last_accessed cho query
    @Query("UPDATE search_history SET last_accessed = :timestamp WHERE query = :query")
    void updateLastAccessed(String query, long timestamp);

    // Xóa lịch sử cũ (quá 30 ngày)
    @Query("DELETE FROM search_history WHERE last_accessed < :timestamp")
    void deleteOldHistory(long timestamp);

    // Đếm số lượng lịch sử
    @Query("SELECT COUNT(*) FROM search_history")
    int getHistoryCount();

    @Query("SELECT COUNT(*) FROM search_history")
    LiveData<Integer> getHistoryCountLive();

    // Lấy top queries (có thể dùng cho suggestions)
    @Query("SELECT DISTINCT query FROM search_history ORDER BY last_accessed DESC LIMIT :limit")
    List<String> getTopQueries(int limit);

    @Query("SELECT DISTINCT query FROM search_history ORDER BY last_accessed DESC LIMIT :limit")
    LiveData<List<String>> getTopQueriesLive(int limit);
}

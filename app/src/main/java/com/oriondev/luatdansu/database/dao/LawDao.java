package com.oriondev.luatdansu.database.dao;

import androidx.lifecycle.LiveData;
import androidx.room.Dao;
import androidx.room.Insert;
import androidx.room.OnConflictStrategy;
import androidx.room.Query;
import androidx.room.Update;
import androidx.room.Delete;

import com.oriondev.luatdansu.database.entity.LawEntity;

import java.util.List;

@Dao
public interface LawDao {

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    void insertAll(List<LawEntity> lawEntities);

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    long insert(LawEntity lawEntity);

    @Update
    void update(LawEntity lawEntity);

    @Delete
    void delete(LawEntity lawEntity);

    @Query("DELETE FROM law_items")
    void deleteAll();

    // Lấy tất cả items theo thứ tự
    @Query("SELECT * FROM law_items ORDER BY order_index ASC")
    LiveData<List<LawEntity>> getAllLawItems();

    @Query("SELECT * FROM law_items ORDER BY order_index ASC")
    List<LawEntity> getAllLawItemsSync();

    // Lấy các chương
    @Query("SELECT * FROM law_items WHERE type = 'chapter' ORDER BY order_index ASC")
    LiveData<List<LawEntity>> getAllChapters();

    @Query("SELECT * FROM law_items WHERE type = 'chapter' ORDER BY order_index ASC")
    List<LawEntity> getAllChaptersSync();

    // Lấy nội dung theo chương
    @Query("SELECT * FROM law_items WHERE chapter_number = :chapterNumber ORDER BY order_index ASC")
    LiveData<List<LawEntity>> getItemsByChapter(String chapterNumber);

    @Query("SELECT * FROM law_items WHERE chapter_number = :chapterNumber ORDER BY order_index ASC")
    List<LawEntity> getItemsByChapterSync(String chapterNumber);

    // Lấy nội dung theo điều
    @Query("SELECT * FROM law_items WHERE article_number = :articleNumber ORDER BY order_index ASC")
    LiveData<List<LawEntity>> getItemsByArticle(String articleNumber);

    @Query("SELECT * FROM law_items WHERE article_number = :articleNumber ORDER BY order_index ASC")
    List<LawEntity> getItemsByArticleSync(String articleNumber);

    // Lấy item theo ID
    @Query("SELECT * FROM law_items WHERE id = :id")
    LiveData<LawEntity> getLawItemById(int id);

    @Query("SELECT * FROM law_items WHERE id = :id")
    LawEntity getLawItemByIdSync(int id);

    // Lấy children của một item
    @Query("SELECT * FROM law_items WHERE parent_id = :parentId ORDER BY order_index ASC")
    LiveData<List<LawEntity>> getChildrenByParentId(int parentId);

    @Query("SELECT * FROM law_items WHERE parent_id = :parentId ORDER BY order_index ASC")
    List<LawEntity> getChildrenByParentIdSync(int parentId);

    // Tìm kiếm full-text
    @Query("SELECT * FROM law_items WHERE search_text LIKE '%' || :query || '%' ORDER BY " +
           "CASE " +
           "  WHEN title LIKE '%' || :query || '%' THEN 1 " +
           "  WHEN content LIKE '%' || :query || '%' THEN 2 " +
           "  ELSE 3 " +
           "END, order_index ASC")
    LiveData<List<LawEntity>> searchLawItems(String query);

    @Query("SELECT * FROM law_items WHERE search_text LIKE '%' || :query || '%' ORDER BY " +
           "CASE " +
           "  WHEN title LIKE '%' || :query || '%' THEN 1 " +
           "  WHEN content LIKE '%' || :query || '%' THEN 2 " +
           "  ELSE 3 " +
           "END, order_index ASC")
    List<LawEntity> searchLawItemsSync(String query);

    // Tìm kiếm trong chương cụ thể
    @Query("SELECT * FROM law_items WHERE chapter_number = :chapterNumber AND search_text LIKE '%' || :query || '%' ORDER BY order_index ASC")
    LiveData<List<LawEntity>> searchInChapter(String chapterNumber, String query);

    // Tìm kiếm theo loại
    @Query("SELECT * FROM law_items WHERE type = :type AND search_text LIKE '%' || :query || '%' ORDER BY order_index ASC")
    LiveData<List<LawEntity>> searchByType(String type, String query);

    // Đếm số lượng items
    @Query("SELECT COUNT(*) FROM law_items")
    int getItemCount();

    @Query("SELECT COUNT(*) FROM law_items WHERE type = :type")
    int getItemCountByType(String type);

    // Kiểm tra database đã có dữ liệu chưa
    @Query("SELECT COUNT(*) FROM law_items WHERE type = 'chapter'")
    int getChapterCount();

    // Lấy item đầu tiên (để hiển thị mặc định)
    @Query("SELECT * FROM law_items ORDER BY order_index ASC LIMIT 1")
    LawEntity getFirstItem();

    // Lấy các điều luật (articles) để hiển thị trong danh sách
    @Query("SELECT * FROM law_items WHERE type = 'article' ORDER BY order_index ASC")
    LiveData<List<LawEntity>> getAllArticles();

    @Query("SELECT * FROM law_items WHERE type = 'article' ORDER BY order_index ASC")
    List<LawEntity> getAllArticlesSync();
}

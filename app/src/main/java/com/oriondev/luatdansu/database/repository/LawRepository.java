package com.oriondev.luatdansu.database.repository;

import android.content.Context;
import androidx.lifecycle.LiveData;
import androidx.lifecycle.MutableLiveData;

import com.oriondev.luatdansu.database.entity.LawEntity;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

public class LawRepository {
    private ExecutorService executor;
    private MutableLiveData<List<LawEntity>> allLawItems;
    private MutableLiveData<List<LawEntity>> allChapters;
    private List<LawEntity> lawItemsCache;

    public LawRepository(Context context) {
        executor = Executors.newFixedThreadPool(4);
        allLawItems = new MutableLiveData<>();
        allChapters = new MutableLiveData<>();
        lawItemsCache = new ArrayList<>();
    }

    // Law Items operations
    public LiveData<List<LawEntity>> getAllLawItems() {
        return allLawItems;
    }

    public LiveData<List<LawEntity>> getAllChapters() {
        return allChapters;
    }

    public LiveData<List<LawEntity>> getItemsByChapter(String chapterNumber) {
        MutableLiveData<List<LawEntity>> result = new MutableLiveData<>();
        executor.execute(() -> {
            List<LawEntity> filtered = new ArrayList<>();
            for (LawEntity item : lawItemsCache) {
                if (chapterNumber.equals(item.getChapterNumber())) {
                    filtered.add(item);
                }
            }
            result.postValue(filtered);
        });
        return result;
    }

    public LiveData<List<LawEntity>> getItemsByArticle(String articleNumber) {
        MutableLiveData<List<LawEntity>> result = new MutableLiveData<>();
        executor.execute(() -> {
            List<LawEntity> filtered = new ArrayList<>();
            for (LawEntity item : lawItemsCache) {
                if (articleNumber.equals(item.getArticleNumber())) {
                    filtered.add(item);
                }
            }
            result.postValue(filtered);
        });
        return result;
    }

    public LiveData<LawEntity> getLawItemById(int id) {
        MutableLiveData<LawEntity> result = new MutableLiveData<>();
        executor.execute(() -> {
            for (LawEntity item : lawItemsCache) {
                if (item.getId() == id) {
                    result.postValue(item);
                    break;
                }
            }
        });
        return result;
    }

    public LiveData<List<LawEntity>> searchLawItems(String query) {
        MutableLiveData<List<LawEntity>> result = new MutableLiveData<>();
        executor.execute(() -> {
            List<LawEntity> filtered = new ArrayList<>();
            String lowerQuery = query.toLowerCase();
            for (LawEntity item : lawItemsCache) {
                if ((item.getTitle() != null && item.getTitle().toLowerCase().contains(lowerQuery)) ||
                    (item.getContent() != null && item.getContent().toLowerCase().contains(lowerQuery)) ||
                    (item.getNumber() != null && item.getNumber().toLowerCase().contains(lowerQuery))) {
                    filtered.add(item);
                }
            }
            result.postValue(filtered);
        });
        return result;
    }

    public void insertLawItems(List<LawEntity> lawEntities) {
        executor.execute(() -> {
            lawItemsCache.clear();
            lawItemsCache.addAll(lawEntities);
            
            // Update LiveData
            allLawItems.postValue(new ArrayList<>(lawItemsCache));
            
            // Filter chapters
            List<LawEntity> chapters = new ArrayList<>();
            for (LawEntity item : lawEntities) {
                if (item.isChapter()) {
                    chapters.add(item);
                }
            }
            allChapters.postValue(chapters);
        });
    }

    public void insertLawItem(LawEntity lawEntity) {
        executor.execute(() -> {
            lawItemsCache.add(lawEntity);
            allLawItems.postValue(new ArrayList<>(lawItemsCache));
        });
    }

    public void deleteAllLawItems() {
        executor.execute(() -> {
            lawItemsCache.clear();
            allLawItems.postValue(new ArrayList<>());
            allChapters.postValue(new ArrayList<>());
        });
    }

    public void checkAndInitializeDatabase(DatabaseCallback callback) {
        executor.execute(() -> {
            boolean isEmpty = lawItemsCache.isEmpty();
            callback.onResult(isEmpty);
        });
    }

    public interface DatabaseCallback {
        void onResult(boolean isEmpty);
    }

    public int getItemCount() {
        return lawItemsCache.size();
    }

    public LiveData<List<LawEntity>> getItemsByType(String type) {
        MutableLiveData<List<LawEntity>> result = new MutableLiveData<>();
        executor.execute(() -> {
            List<LawEntity> filtered = new ArrayList<>();
            for (LawEntity item : lawItemsCache) {
                if (type.equals(item.getType())) {
                    filtered.add(item);
                }
            }
            result.postValue(filtered);
        });
        return result;
    }

    public LiveData<List<String>> getAllChapterNumbers() {
        MutableLiveData<List<String>> result = new MutableLiveData<>();
        executor.execute(() -> {
            List<String> chapterNumbers = new ArrayList<>();
            for (LawEntity item : lawItemsCache) {
                if (item.getChapterNumber() != null && !chapterNumbers.contains(item.getChapterNumber())) {
                    chapterNumbers.add(item.getChapterNumber());
                }
            }
            result.postValue(chapterNumbers);
        });
        return result;
    }

    public LiveData<List<String>> getAllArticleNumbers() {
        MutableLiveData<List<String>> result = new MutableLiveData<>();
        executor.execute(() -> {
            List<String> articleNumbers = new ArrayList<>();
            for (LawEntity item : lawItemsCache) {
                if (item.getArticleNumber() != null && !articleNumbers.contains(item.getArticleNumber())) {
                    articleNumbers.add(item.getArticleNumber());
                }
            }
            result.postValue(articleNumbers);
        });
        return result;
    }
}

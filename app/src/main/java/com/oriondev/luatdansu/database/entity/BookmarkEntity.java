package com.oriondev.luatdansu.database.entity;

import androidx.room.Entity;
import androidx.room.PrimaryKey;
import androidx.room.ColumnInfo;

@Entity(tableName = "bookmarks")
public class BookmarkEntity {
    @PrimaryKey(autoGenerate = true)
    private int id;

    @ColumnInfo(name = "law_item_id")
    private int lawItemId; // ID của LawEntity được bookmark

    @ColumnInfo(name = "title")
    private String title; // Tiêu đề để hiển thị

    @ColumnInfo(name = "content_preview")
    private String contentPreview; // Preview nội dung

    @ColumnInfo(name = "chapter_number")
    private String chapterNumber;

    @ColumnInfo(name = "article_number")
    private String articleNumber;

    @ColumnInfo(name = "created_at")
    private long createdAt; // Timestamp khi tạo bookmark

    @ColumnInfo(name = "note")
    private String note; // <PERSON>hi chú của người dùng

    // Constructors
    public BookmarkEntity() {}

    public BookmarkEntity(int lawItemId, String title, String contentPreview, 
                         String chapterNumber, String articleNumber, String note) {
        this.lawItemId = lawItemId;
        this.title = title;
        this.contentPreview = contentPreview;
        this.chapterNumber = chapterNumber;
        this.articleNumber = articleNumber;
        this.note = note;
        this.createdAt = System.currentTimeMillis();
    }

    // Getters and Setters
    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public int getLawItemId() {
        return lawItemId;
    }

    public void setLawItemId(int lawItemId) {
        this.lawItemId = lawItemId;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getContentPreview() {
        return contentPreview;
    }

    public void setContentPreview(String contentPreview) {
        this.contentPreview = contentPreview;
    }

    public String getChapterNumber() {
        return chapterNumber;
    }

    public void setChapterNumber(String chapterNumber) {
        this.chapterNumber = chapterNumber;
    }

    public String getArticleNumber() {
        return articleNumber;
    }

    public void setArticleNumber(String articleNumber) {
        this.articleNumber = articleNumber;
    }

    public long getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(long createdAt) {
        this.createdAt = createdAt;
    }

    public String getNote() {
        return note;
    }

    public void setNote(String note) {
        this.note = note;
    }

    // Helper methods
    public String getLocationText() {
        StringBuilder sb = new StringBuilder();
        if (chapterNumber != null && !chapterNumber.isEmpty()) {
            sb.append("Chương ").append(chapterNumber);
        }
        if (articleNumber != null && !articleNumber.isEmpty()) {
            if (sb.length() > 0) {
                sb.append(", ");
            }
            sb.append("Điều ").append(articleNumber);
        }
        return sb.toString();
    }
}

package com.oriondev.luatdansu.database.dao;

import androidx.lifecycle.LiveData;
import androidx.room.Dao;
import androidx.room.Insert;
import androidx.room.OnConflictStrategy;
import androidx.room.Query;
import androidx.room.Update;
import androidx.room.Delete;

import com.oriondev.luatdansu.database.entity.BookmarkEntity;

import java.util.List;

@Dao
public interface BookmarkDao {

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    long insert(BookmarkEntity bookmark);

    @Update
    void update(BookmarkEntity bookmark);

    @Delete
    void delete(BookmarkEntity bookmark);

    @Query("DELETE FROM bookmarks WHERE law_item_id = :lawItemId")
    void deleteByLawItemId(int lawItemId);

    @Query("DELETE FROM bookmarks")
    void deleteAll();

    // Lấy tất cả bookmarks theo thời gian tạo (mới nhất trước)
    @Query("SELECT * FROM bookmarks ORDER BY created_at DESC")
    LiveData<List<BookmarkEntity>> getAllBookmarks();

    @Query("SELECT * FROM bookmarks ORDER BY created_at DESC")
    List<BookmarkEntity> getAllBookmarksSync();

    // Kiểm tra item đã được bookmark chưa
    @Query("SELECT COUNT(*) FROM bookmarks WHERE law_item_id = :lawItemId")
    int isBookmarked(int lawItemId);

    @Query("SELECT COUNT(*) FROM bookmarks WHERE law_item_id = :lawItemId")
    LiveData<Integer> isBookmarkedLive(int lawItemId);

    // Lấy bookmark theo law_item_id
    @Query("SELECT * FROM bookmarks WHERE law_item_id = :lawItemId LIMIT 1")
    BookmarkEntity getBookmarkByLawItemId(int lawItemId);

    @Query("SELECT * FROM bookmarks WHERE law_item_id = :lawItemId LIMIT 1")
    LiveData<BookmarkEntity> getBookmarkByLawItemIdLive(int lawItemId);

    // Lấy bookmark theo ID
    @Query("SELECT * FROM bookmarks WHERE id = :id")
    BookmarkEntity getBookmarkById(int id);

    @Query("SELECT * FROM bookmarks WHERE id = :id")
    LiveData<BookmarkEntity> getBookmarkByIdLive(int id);

    // Tìm kiếm trong bookmarks
    @Query("SELECT * FROM bookmarks WHERE title LIKE '%' || :query || '%' OR content_preview LIKE '%' || :query || '%' OR note LIKE '%' || :query || '%' ORDER BY created_at DESC")
    LiveData<List<BookmarkEntity>> searchBookmarks(String query);

    @Query("SELECT * FROM bookmarks WHERE title LIKE '%' || :query || '%' OR content_preview LIKE '%' || :query || '%' OR note LIKE '%' || :query || '%' ORDER BY created_at DESC")
    List<BookmarkEntity> searchBookmarksSync(String query);

    // Lấy bookmarks theo chương
    @Query("SELECT * FROM bookmarks WHERE chapter_number = :chapterNumber ORDER BY created_at DESC")
    LiveData<List<BookmarkEntity>> getBookmarksByChapter(String chapterNumber);

    // Lấy bookmarks theo điều
    @Query("SELECT * FROM bookmarks WHERE article_number = :articleNumber ORDER BY created_at DESC")
    LiveData<List<BookmarkEntity>> getBookmarksByArticle(String articleNumber);

    // Đếm số lượng bookmarks
    @Query("SELECT COUNT(*) FROM bookmarks")
    int getBookmarkCount();

    @Query("SELECT COUNT(*) FROM bookmarks")
    LiveData<Integer> getBookmarkCountLive();

    // Lấy bookmarks gần đây (trong 7 ngày)
    @Query("SELECT * FROM bookmarks WHERE created_at > :timestamp ORDER BY created_at DESC")
    LiveData<List<BookmarkEntity>> getRecentBookmarks(long timestamp);

    // Lấy top bookmarks (có thể mở rộng để thêm tính năng rating)
    @Query("SELECT * FROM bookmarks ORDER BY created_at DESC LIMIT :limit")
    LiveData<List<BookmarkEntity>> getTopBookmarks(int limit);
}

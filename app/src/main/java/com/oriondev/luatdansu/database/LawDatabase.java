package com.oriondev.luatdansu.database;

import android.content.Context;
import androidx.room.Database;
import androidx.room.Room;
import androidx.room.RoomDatabase;
import androidx.room.migration.Migration;
import androidx.sqlite.db.SupportSQLiteDatabase;

import com.oriondev.luatdansu.database.dao.BookmarkDao;
import com.oriondev.luatdansu.database.dao.LawDao;
import com.oriondev.luatdansu.database.dao.SearchHistoryDao;
import com.oriondev.luatdansu.database.entity.BookmarkEntity;
import com.oriondev.luatdansu.database.entity.LawEntity;
import com.oriondev.luatdansu.database.entity.SearchHistoryEntity;

@Database(
    entities = {LawEntity.class, BookmarkEntity.class, SearchHistoryEntity.class},
    version = 1,
    exportSchema = false
)
public abstract class LawDatabase extends RoomDatabase {

    private static final String DATABASE_NAME = "law_database";
    private static volatile LawDatabase INSTANCE;

    public abstract LawDao lawDao();
    public abstract BookmarkDao bookmarkDao();
    public abstract SearchHistoryDao searchHistoryDao();

    public static LawDatabase getInstance(Context context) {
        if (INSTANCE == null) {
            synchronized (LawDatabase.class) {
                if (INSTANCE == null) {
                    INSTANCE = Room.databaseBuilder(
                            context.getApplicationContext(),
                            LawDatabase.class,
                            DATABASE_NAME
                    )
                    .fallbackToDestructiveMigration() // Cho phép xóa và tạo lại DB khi cần
                    .build();
                }
            }
        }
        return INSTANCE;
    }

    // Migration cho các version tương lai
    static final Migration MIGRATION_1_2 = new Migration(1, 2) {
        @Override
        public void migrate(SupportSQLiteDatabase database) {
            // Thêm migration logic khi cần thiết
        }
    };

    // Callback để thực hiện các tác vụ khi database được tạo
    private static RoomDatabase.Callback roomCallback = new RoomDatabase.Callback() {
        @Override
        public void onCreate(SupportSQLiteDatabase db) {
            super.onCreate(db);
            // Có thể thêm logic khởi tạo dữ liệu ở đây
        }

        @Override
        public void onOpen(SupportSQLiteDatabase db) {
            super.onOpen(db);
            // Logic khi database được mở
        }
    };

    // Method để xóa toàn bộ database (dùng cho testing hoặc reset)
    public static void destroyInstance() {
        INSTANCE = null;
    }
}

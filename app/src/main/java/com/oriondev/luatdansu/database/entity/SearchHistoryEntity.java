package com.oriondev.luatdansu.database.entity;

import androidx.room.Entity;
import androidx.room.PrimaryKey;
import androidx.room.ColumnInfo;

@Entity(tableName = "search_history")
public class SearchHistoryEntity {
    @PrimaryKey(autoGenerate = true)
    private int id;

    @ColumnInfo(name = "query")
    private String query; // Từ khóa tìm kiếm

    @ColumnInfo(name = "result_count")
    private int resultCount; // Số kết quả tìm được

    @ColumnInfo(name = "searched_at")
    private long searchedAt; // Timestamp khi tìm kiếm

    @ColumnInfo(name = "last_accessed")
    private long lastAccessed; // Lần cuối truy cập

    // Constructors
    public SearchHistoryEntity() {}

    public SearchHistoryEntity(String query, int resultCount) {
        this.query = query;
        this.resultCount = resultCount;
        this.searchedAt = System.currentTimeMillis();
        this.lastAccessed = System.currentTimeMillis();
    }

    // Getters and Setters
    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getQuery() {
        return query;
    }

    public void setQuery(String query) {
        this.query = query;
    }

    public int getResultCount() {
        return resultCount;
    }

    public void setResultCount(int resultCount) {
        this.resultCount = resultCount;
    }

    public long getSearchedAt() {
        return searchedAt;
    }

    public void setSearchedAt(long searchedAt) {
        this.searchedAt = searchedAt;
    }

    public long getLastAccessed() {
        return lastAccessed;
    }

    public void setLastAccessed(long lastAccessed) {
        this.lastAccessed = lastAccessed;
    }

    public void updateLastAccessed() {
        this.lastAccessed = System.currentTimeMillis();
    }
}

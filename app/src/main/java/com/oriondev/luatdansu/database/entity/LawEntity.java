package com.oriondev.luatdansu.database.entity;

import androidx.room.Entity;
import androidx.room.PrimaryKey;
import androidx.room.ColumnInfo;

@Entity(tableName = "law_items")
public class LawEntity {
    @PrimaryKey(autoGenerate = true)
    private int id;

    @ColumnInfo(name = "type")
    private String type; // "chapter", "article", "clause", "content"

    @ColumnInfo(name = "number")
    private String number; // S<PERSON> thứ tự (I, II, 1, 2, a, b, etc.)

    @ColumnInfo(name = "title")
    private String title; // Tiêu đề

    @ColumnInfo(name = "content")
    private String content; // Nội dung

    @ColumnInfo(name = "parent_id")
    private Integer parentId; // ID của item cha

    @ColumnInfo(name = "chapter_number")
    private String chapterNumber; // Số chương để dễ tìm kiếm

    @ColumnInfo(name = "article_number")
    private String articleNumber; // <PERSON><PERSON> điều để dễ tìm kiếm

    @ColumnInfo(name = "order_index")
    private int orderIndex; // Thứ tự hiển thị

    @ColumnInfo(name = "search_text")
    private String searchText; // Text để tìm kiếm (title + content)

    // Constructors
    public LawEntity() {}

    @androidx.room.Ignore
    public LawEntity(String type, String number, String title, String content,
                     Integer parentId, String chapterNumber, String articleNumber,
                     int orderIndex) {
        this.type = type;
        this.number = number;
        this.title = title;
        this.content = content;
        this.parentId = parentId;
        this.chapterNumber = chapterNumber;
        this.articleNumber = articleNumber;
        this.orderIndex = orderIndex;
        this.searchText = (title != null ? title : "") + " " + (content != null ? content : "");
    }

    // Getters and Setters
    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getNumber() {
        return number;
    }

    public void setNumber(String number) {
        this.number = number;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
        updateSearchText();
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
        updateSearchText();
    }

    public Integer getParentId() {
        return parentId;
    }

    public void setParentId(Integer parentId) {
        this.parentId = parentId;
    }

    public String getChapterNumber() {
        return chapterNumber;
    }

    public void setChapterNumber(String chapterNumber) {
        this.chapterNumber = chapterNumber;
    }

    public String getArticleNumber() {
        return articleNumber;
    }

    public void setArticleNumber(String articleNumber) {
        this.articleNumber = articleNumber;
    }

    public int getOrderIndex() {
        return orderIndex;
    }

    public void setOrderIndex(int orderIndex) {
        this.orderIndex = orderIndex;
    }

    public String getSearchText() {
        return searchText;
    }

    public void setSearchText(String searchText) {
        this.searchText = searchText;
    }

    private void updateSearchText() {
        this.searchText = (title != null ? title : "") + " " + (content != null ? content : "");
    }

    // Helper methods
    public boolean isChapter() {
        return "chapter".equals(type);
    }

    public boolean isArticle() {
        return "article".equals(type);
    }

    public boolean isClause() {
        return "clause".equals(type);
    }

    public boolean isContent() {
        return "content".equals(type);
    }

    public String getDisplayTitle() {
        if (title != null && !title.isEmpty()) {
            if (number != null && !number.isEmpty()) {
                return number + ". " + title;
            }
            return title;
        }
        return content;
    }

    public String getFullDisplayText() {
        StringBuilder sb = new StringBuilder();
        if (title != null && !title.isEmpty()) {
            if (number != null && !number.isEmpty()) {
                sb.append(number).append(". ");
            }
            sb.append(title);
            if (content != null && !content.isEmpty()) {
                sb.append("\n").append(content);
            }
        } else if (content != null && !content.isEmpty()) {
            sb.append(content);
        }
        return sb.toString();
    }
}

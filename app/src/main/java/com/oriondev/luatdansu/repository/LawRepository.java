package com.oriondev.luatdansu.repository;

import android.content.Context;
import android.os.AsyncTask;
import androidx.lifecycle.LiveData;

import com.oriondev.luatdansu.database.LawDatabase;
import com.oriondev.luatdansu.database.dao.BookmarkDao;
import com.oriondev.luatdansu.database.dao.LawDao;
import com.oriondev.luatdansu.database.dao.SearchHistoryDao;
import com.oriondev.luatdansu.database.entity.BookmarkEntity;
import com.oriondev.luatdansu.database.entity.LawEntity;
import com.oriondev.luatdansu.database.entity.SearchHistoryEntity;

import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

public class LawRepository {
    private LawDao lawDao;
    private BookmarkDao bookmarkDao;
    private SearchHistoryDao searchHistoryDao;
    private ExecutorService executor;

    private LiveData<List<LawEntity>> allLawItems;
    private LiveData<List<LawEntity>> allChapters;
    private LiveData<List<BookmarkEntity>> allBookmarks;
    private LiveData<List<SearchHistoryEntity>> recentSearchHistory;

    public LawRepository(Context context) {
        LawDatabase database = LawDatabase.getInstance(context);
        lawDao = database.lawDao();
        bookmarkDao = database.bookmarkDao();
        searchHistoryDao = database.searchHistoryDao();
        executor = Executors.newFixedThreadPool(4);

        allLawItems = lawDao.getAllLawItems();
        allChapters = lawDao.getAllChapters();
        allBookmarks = bookmarkDao.getAllBookmarks();
        recentSearchHistory = searchHistoryDao.getRecentSearchHistory(20);
    }

    // Law Items operations
    public LiveData<List<LawEntity>> getAllLawItems() {
        return allLawItems;
    }

    public LiveData<List<LawEntity>> getAllChapters() {
        return allChapters;
    }

    public LiveData<List<LawEntity>> getItemsByChapter(String chapterNumber) {
        return lawDao.getItemsByChapter(chapterNumber);
    }

    public LiveData<List<LawEntity>> getItemsByArticle(String articleNumber) {
        return lawDao.getItemsByArticle(articleNumber);
    }

    public LiveData<LawEntity> getLawItemById(int id) {
        return lawDao.getLawItemById(id);
    }

    public LiveData<List<LawEntity>> searchLawItems(String query) {
        return lawDao.searchLawItems(query);
    }

    public void insertLawItems(List<LawEntity> lawEntities) {
        executor.execute(() -> lawDao.insertAll(lawEntities));
    }

    public void insertLawItem(LawEntity lawEntity) {
        executor.execute(() -> lawDao.insert(lawEntity));
    }

    public void deleteAllLawItems() {
        executor.execute(() -> lawDao.deleteAll());
    }

    // Bookmark operations
    public LiveData<List<BookmarkEntity>> getAllBookmarks() {
        return allBookmarks;
    }

    public LiveData<Integer> isBookmarked(int lawItemId) {
        return bookmarkDao.isBookmarkedLive(lawItemId);
    }

    public void insertBookmark(BookmarkEntity bookmark) {
        executor.execute(() -> bookmarkDao.insert(bookmark));
    }

    public void deleteBookmark(BookmarkEntity bookmark) {
        executor.execute(() -> bookmarkDao.delete(bookmark));
    }

    public void deleteBookmarkByLawItemId(int lawItemId) {
        executor.execute(() -> bookmarkDao.deleteByLawItemId(lawItemId));
    }

    public LiveData<List<BookmarkEntity>> searchBookmarks(String query) {
        return bookmarkDao.searchBookmarks(query);
    }

    // Search History operations
    public LiveData<List<SearchHistoryEntity>> getRecentSearchHistory() {
        return recentSearchHistory;
    }

    public LiveData<List<String>> getTopQueries(int limit) {
        return searchHistoryDao.getTopQueriesLive(limit);
    }

    public void insertSearchHistory(String query, int resultCount) {
        executor.execute(() -> {
            SearchHistoryEntity existing = searchHistoryDao.getSearchHistoryByQuery(query);
            if (existing != null) {
                existing.updateLastAccessed();
                existing.setResultCount(resultCount);
                searchHistoryDao.update(existing);
            } else {
                SearchHistoryEntity newHistory = new SearchHistoryEntity(query, resultCount);
                searchHistoryDao.insert(newHistory);
            }
        });
    }

    public void deleteSearchHistory(SearchHistoryEntity searchHistory) {
        executor.execute(() -> searchHistoryDao.delete(searchHistory));
    }

    public void deleteAllSearchHistory() {
        executor.execute(() -> searchHistoryDao.deleteAll());
    }

    // Utility methods
    public void checkAndInitializeDatabase(DatabaseInitCallback callback) {
        executor.execute(() -> {
            int chapterCount = lawDao.getChapterCount();
            boolean isEmpty = chapterCount == 0;
            
            // Post result back to main thread
            if (callback != null) {
                callback.onResult(isEmpty);
            }
        });
    }

    public interface DatabaseInitCallback {
        void onResult(boolean isEmpty);
    }

    // Cleanup
    public void cleanup() {
        if (executor != null && !executor.isShutdown()) {
            executor.shutdown();
        }
    }
}

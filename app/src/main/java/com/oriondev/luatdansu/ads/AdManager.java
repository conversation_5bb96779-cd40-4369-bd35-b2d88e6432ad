package com.oriondev.luatdansu.ads;

import android.app.Activity;
import android.content.Context;
import android.content.SharedPreferences;
import android.util.DisplayMetrics;
import android.util.Log;
import android.view.Display;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;

import com.google.android.gms.ads.AdListener;
import com.google.android.gms.ads.AdLoader;
import com.google.android.gms.ads.AdRequest;
import com.google.android.gms.ads.AdSize;
import com.google.android.gms.ads.AdView;
import com.google.android.gms.ads.FullScreenContentCallback;
import com.google.android.gms.ads.LoadAdError;
import com.google.android.gms.ads.MobileAds;
import com.google.android.gms.ads.interstitial.InterstitialAd;
import com.google.android.gms.ads.interstitial.InterstitialAdLoadCallback;
import com.google.android.gms.ads.nativead.NativeAd;
import com.google.android.gms.ads.nativead.NativeAdOptions;
import com.google.android.gms.ads.nativead.NativeAdView;
import com.google.android.ump.ConsentForm;
import com.google.android.ump.ConsentInformation;
import com.google.android.ump.ConsentRequestParameters;
import com.google.android.ump.FormError;
import com.google.android.ump.UserMessagingPlatform;

import com.oriondev.luatdansu.R;

public class AdManager {
    private static final String TAG = "AdManager";
    private static AdManager instance;
    
    private Context context;
    private InterstitialAd interstitialAd;
    private ConsentInformation consentInformation;
    private ConsentForm consentForm;
    
    // Frequency capping for interstitial ads
    private int interstitialShowCount = 0;
    private static final int INTERSTITIAL_FREQUENCY = 3;
    
    // Callbacks
    public interface AdLoadCallback {
        void onAdLoaded();
        void onAdFailedToLoad(String error);
    }
    
    public interface InterstitialAdCallback {
        void onAdShown();
        void onAdDismissed();
        void onAdFailedToShow(String error);
    }
    
    public interface NativeAdCallback {
        void onNativeAdLoaded(NativeAd nativeAd);
        void onNativeAdFailedToLoad(String error);
    }
    
    private AdManager(Context context) {
        this.context = context.getApplicationContext();
        initializeAds();
    }
    
    public static synchronized AdManager getInstance(Context context) {
        if (instance == null) {
            instance = new AdManager(context);
        }
        return instance;
    }
    
    private void initializeAds() {
        // Initialize Mobile Ads SDK
        MobileAds.initialize(context, initializationStatus -> {
            Log.d(TAG, "AdMob SDK initialized");
            loadInterstitialAd();
        });
        
        // Initialize consent information
        initializeConsent();
    }
    
    private void initializeConsent() {
        consentInformation = UserMessagingPlatform.getConsentInformation(context);
        
        ConsentRequestParameters params = new ConsentRequestParameters.Builder()
                .setTagForUnderAgeOfConsent(false)
                .build();
        
        consentInformation.requestConsentInfoUpdate(
                (Activity) context,
                params,
                () -> {
                    if (consentInformation.isConsentFormAvailable()) {
                        loadConsentForm();
                    }
                },
                requestConsentError -> {
                    Log.e(TAG, "Consent request error: " + requestConsentError.getMessage());
                }
        );
    }
    
    private void loadConsentForm() {
        UserMessagingPlatform.loadConsentForm(
                context,
                consentForm -> {
                    this.consentForm = consentForm;
                    if (consentInformation.getConsentStatus() == ConsentInformation.ConsentStatus.REQUIRED) {
                        consentForm.show((Activity) context, formError -> {
                            if (formError != null) {
                                Log.e(TAG, "Consent form error: " + formError.getMessage());
                            }
                            loadConsentForm();
                        });
                    }
                },
                formError -> Log.e(TAG, "Consent form load error: " + formError.getMessage())
        );
    }
    
    // Adaptive Banner Methods
    public AdView createAdaptiveBannerAd(Activity activity) {
        AdView adView = new AdView(activity);
        adView.setAdUnitId(activity.getString(R.string.admob_banner_id));
        
        // Get adaptive banner size
        AdSize adSize = getAdaptiveBannerSize(activity);
        adView.setAdSize(adSize);
        
        return adView;
    }
    
    private AdSize getAdaptiveBannerSize(Activity activity) {
        Display display = activity.getWindowManager().getDefaultDisplay();
        DisplayMetrics outMetrics = new DisplayMetrics();
        display.getMetrics(outMetrics);
        
        float widthPixels = outMetrics.widthPixels;
        float density = outMetrics.density;
        int adWidth = (int) (widthPixels / density);
        
        return AdSize.getCurrentOrientationAnchoredAdaptiveBannerAdSize(activity, adWidth);
    }
    
    public void loadBannerAd(AdView adView, AdLoadCallback callback) {
        AdRequest adRequest = new AdRequest.Builder().build();
        
        adView.setAdListener(new AdListener() {
            @Override
            public void onAdLoaded() {
                super.onAdLoaded();
                Log.d(TAG, "Banner ad loaded");
                if (callback != null) {
                    callback.onAdLoaded();
                }
            }
            
            @Override
            public void onAdFailedToLoad(@NonNull LoadAdError loadAdError) {
                super.onAdFailedToLoad(loadAdError);
                Log.e(TAG, "Banner ad failed to load: " + loadAdError.getMessage());
                if (callback != null) {
                    callback.onAdFailedToLoad(loadAdError.getMessage());
                }
            }
            
            @Override
            public void onAdClicked() {
                super.onAdClicked();
                Log.d(TAG, "Banner ad clicked");
            }
        });
        
        adView.loadAd(adRequest);
    }
    
    // Interstitial Ad Methods
    private void loadInterstitialAd() {
        AdRequest adRequest = new AdRequest.Builder().build();
        
        InterstitialAd.load(context, context.getString(R.string.admob_test_interstitial_id),
                adRequest, new InterstitialAdLoadCallback() {
                    @Override
                    public void onAdLoaded(@NonNull InterstitialAd ad) {
                        interstitialAd = ad;
                        Log.d(TAG, "Interstitial ad loaded");
                        
                        interstitialAd.setFullScreenContentCallback(new FullScreenContentCallback() {
                            @Override
                            public void onAdDismissedFullScreenContent() {
                                super.onAdDismissedFullScreenContent();
                                Log.d(TAG, "Interstitial ad dismissed");
                                interstitialAd = null;
                                loadInterstitialAd(); // Load next ad
                            }
                            
                            @Override
                            public void onAdFailedToShowFullScreenContent(@NonNull com.google.android.gms.ads.AdError adError) {
                                super.onAdFailedToShowFullScreenContent(adError);
                                Log.e(TAG, "Interstitial ad failed to show: " + adError.getMessage());
                                interstitialAd = null;
                            }
                            
                            @Override
                            public void onAdShowedFullScreenContent() {
                                super.onAdShowedFullScreenContent();
                                Log.d(TAG, "Interstitial ad showed");
                            }
                        });
                    }
                    
                    @Override
                    public void onAdFailedToLoad(@NonNull LoadAdError loadAdError) {
                        Log.e(TAG, "Interstitial ad failed to load: " + loadAdError.getMessage());
                        interstitialAd = null;
                    }
                });
    }
    
    public void showInterstitialAd(Activity activity, InterstitialAdCallback callback) {
        interstitialShowCount++;
        
        // Check frequency capping
        if (interstitialShowCount % INTERSTITIAL_FREQUENCY != 0) {
            Log.d(TAG, "Interstitial ad skipped due to frequency capping");
            return;
        }
        
        if (interstitialAd != null) {
            interstitialAd.show(activity);
            if (callback != null) {
                callback.onAdShown();
            }
        } else {
            Log.d(TAG, "Interstitial ad not ready");
            if (callback != null) {
                callback.onAdFailedToShow("Ad not ready");
            }
            loadInterstitialAd(); // Try to load again
        }
    }
    
    // Native Ad Methods
    public void loadNativeAd(NativeAdCallback callback) {
        AdLoader adLoader = new AdLoader.Builder(context, context.getString(R.string.admob_test_native_id))
                .forNativeAd(nativeAd -> {
                    Log.d(TAG, "Native ad loaded");
                    if (callback != null) {
                        callback.onNativeAdLoaded(nativeAd);
                    }
                })
                .withAdListener(new AdListener() {
                    @Override
                    public void onAdFailedToLoad(@NonNull LoadAdError adError) {
                        Log.e(TAG, "Native ad failed to load: " + adError.getMessage());
                        if (callback != null) {
                            callback.onNativeAdFailedToLoad(adError.getMessage());
                        }
                    }
                })
                .withNativeAdOptions(new NativeAdOptions.Builder()
                        .setReturnUrlsForImageAssets(true)
                        .build())
                .build();
        
        adLoader.loadAd(new AdRequest.Builder().build());
    }
    
    public static void populateNativeAdView(NativeAd nativeAd, NativeAdView adView) {
        // Set the media view
        adView.setMediaView(adView.findViewById(R.id.ad_media));
        
        // Set other ad assets
        adView.setHeadlineView(adView.findViewById(R.id.ad_headline));
        adView.setBodyView(adView.findViewById(R.id.ad_body));
        adView.setCallToActionView(adView.findViewById(R.id.ad_call_to_action));
        adView.setIconView(adView.findViewById(R.id.ad_app_icon));
        adView.setAdvertiserView(adView.findViewById(R.id.ad_advertiser));
        
        // Populate the views with ad content
        if (nativeAd.getHeadline() != null) {
            ((TextView) adView.getHeadlineView()).setText(nativeAd.getHeadline());
        }
        
        if (nativeAd.getBody() != null) {
            ((TextView) adView.getBodyView()).setText(nativeAd.getBody());
        }
        
        if (nativeAd.getCallToAction() != null) {
            ((Button) adView.getCallToActionView()).setText(nativeAd.getCallToAction());
        }
        
        if (nativeAd.getIcon() != null) {
            ((ImageView) adView.getIconView()).setImageDrawable(nativeAd.getIcon().getDrawable());
        }
        
        if (nativeAd.getAdvertiser() != null) {
            ((TextView) adView.getAdvertiserView()).setText(nativeAd.getAdvertiser());
        }
        
        // Register the native ad with the view
        adView.setNativeAd(nativeAd);
    }
    
    // Utility Methods
    public boolean isInterstitialReady() {
        return interstitialAd != null;
    }
    
    public void resetInterstitialCounter() {
        interstitialShowCount = 0;
    }
}
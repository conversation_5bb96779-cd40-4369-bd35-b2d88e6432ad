package com.oriondev.luatdansu;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.SharedPreferences;
import android.net.ConnectivityManager;
import android.net.NetworkInfo;
import android.os.Bundle;
import android.view.MenuItem;
import android.view.View;
import android.webkit.WebChromeClient;
import android.webkit.WebResourceError;
import android.webkit.WebResourceRequest;
import android.webkit.WebSettings;
import android.webkit.WebView;
import android.webkit.WebViewClient;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.appcompat.app.ActionBarDrawerToggle;
import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.widget.SearchView;
import androidx.core.splashscreen.SplashScreen;
import androidx.core.view.GravityCompat;
import androidx.core.view.WindowCompat;
import androidx.drawerlayout.widget.DrawerLayout;

import com.google.android.gms.ads.AdRequest;
import com.google.android.gms.ads.AdView;
import com.google.android.gms.ads.MobileAds;
import com.google.android.material.appbar.MaterialToolbar;
import com.google.android.material.button.MaterialButton;
import com.google.android.material.card.MaterialCardView;
import com.google.android.material.navigation.NavigationView;
import com.google.android.material.progressindicator.LinearProgressIndicator;
import com.google.android.material.snackbar.Snackbar;

public class MainActivityWebView extends AppCompatActivity implements NavigationView.OnNavigationItemSelectedListener {

    private WebView webView;
    private SearchView searchView;
    private MaterialButton btnSearch, btnHome, btnBookmark, btnShare, btnSettings;
    private AdView adView;
    private LinearProgressIndicator progressBar;
    private MaterialCardView adContainer;
    private MaterialToolbar toolbar;
    private DrawerLayout drawerLayout;
    private NavigationView navigationView;
    private ActionBarDrawerToggle drawerToggle;
    private BroadcastReceiver connectivityReceiver;
    private SharedPreferences preferences;

    // URL mặc định (trang Home của ứng dụng)
    private static final String HOME_URL = "file:///android_asset/luat.html";

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        // Handle the splash screen transition
        SplashScreen splashScreen = SplashScreen.installSplashScreen(this);

        super.onCreate(savedInstanceState);

        // Enable edge-to-edge display
        WindowCompat.setDecorFitsSystemWindows(getWindow(), false);

        setContentView(R.layout.activity_main);

        // Initialize SharedPreferences
        preferences = getSharedPreferences("app_settings", MODE_PRIVATE);

        // Khởi tạo AdMob SDK
        MobileAds.initialize(this, initializationStatus -> {});

        // Ánh xạ các thành phần UI
        initializeViews();

        // Setup toolbar and navigation
        setupToolbarAndNavigation();

        // Thiết lập BroadcastReceiver để lắng nghe thay đổi kết nối mạng
        setupConnectivityReceiver();

        // Cấu hình WebView
        setupWebView();

        // Thiết lập các sự kiện
        setupEventListeners();

        // Cập nhật trạng thái quảng cáo ban đầu
        updateAdVisibility();
    }

    private void initializeViews() {
        // Initialize main views
        webView = findViewById(R.id.recyclerView); // Temporary fix - should use proper WebView layout
        toolbar = findViewById(R.id.toolbar);
        drawerLayout = findViewById(R.id.drawer_layout);
        navigationView = findViewById(R.id.navigation_view);
        searchView = findViewById(R.id.search_view);

        // Initialize bottom navigation buttons
        btnSearch = findViewById(R.id.btnSearch);
        btnHome = findViewById(R.id.btnHome);
        btnBookmark = findViewById(R.id.btnBookmark);
        btnShare = findViewById(R.id.btnShare);
        btnSettings = findViewById(R.id.btnSettings);

        // Initialize ad views
        adView = findViewById(R.id.adView);
        progressBar = findViewById(R.id.progressBar);
        adContainer = findViewById(R.id.adContainer);

        // Đặt visibility ban đầu cho container quảng cáo là GONE
        if (adContainer != null) {
            adContainer.setVisibility(View.GONE);
        }
    }

    private void setupToolbarAndNavigation() {
        // Setup toolbar
        setSupportActionBar(toolbar);

        // Setup navigation drawer
        drawerToggle = new ActionBarDrawerToggle(
            this, drawerLayout, toolbar,
            R.string.navigation_drawer_open,
            R.string.navigation_drawer_close
        );
        drawerLayout.addDrawerListener(drawerToggle);
        drawerToggle.syncState();

        // Setup navigation view
        navigationView.setNavigationItemSelectedListener(this);
    }

    private void setupConnectivityReceiver() {
        connectivityReceiver = new BroadcastReceiver() {
            @Override
            public void onReceive(Context context, Intent intent) {
                if (ConnectivityManager.CONNECTIVITY_ACTION.equals(intent.getAction())) {
                    updateAdVisibility();
                }
            }
        };
        IntentFilter filter = new IntentFilter(ConnectivityManager.CONNECTIVITY_ACTION);
        registerReceiver(connectivityReceiver, filter);
    }

    private void setupWebView() {
        WebSettings webSettings = webView.getSettings();

        // Cấu hình bảo mật
        webSettings.setJavaScriptEnabled(true);
        webSettings.setDomStorageEnabled(true);
        webSettings.setAllowFileAccess(true);
        webSettings.setAllowContentAccess(true);
        webSettings.setAllowFileAccessFromFileURLs(false);
        webSettings.setAllowUniversalAccessFromFileURLs(false);

        // Cấu hình hiệu suất
        webSettings.setCacheMode(WebSettings.LOAD_DEFAULT);
        webSettings.setMixedContentMode(WebSettings.MIXED_CONTENT_ALWAYS_ALLOW);

        // Apply saved text size
        int savedTextSize = preferences.getInt("web_text_size", 100);
        webSettings.setTextZoom(savedTextSize);

        // Thiết lập WebViewClient
        webView.setWebViewClient(new WebViewClient() {
            @Override
            public void onPageStarted(WebView view, String url, android.graphics.Bitmap favicon) {
                super.onPageStarted(view, url, favicon);
                progressBar.setVisibility(View.VISIBLE);
            }

            @Override
            public void onPageFinished(WebView view, String url) {
                super.onPageFinished(view, url);
                progressBar.setVisibility(View.GONE);
                updateAdVisibility();
            }

            @Override
            public void onReceivedError(WebView view, WebResourceRequest request, WebResourceError error) {
                super.onReceivedError(view, request, error);
                progressBar.setVisibility(View.GONE);
                showError("Không thể tải trang. Vui lòng kiểm tra kết nối internet.");
                updateAdVisibility();
            }
        });

        // Thiết lập WebChromeClient
        webView.setWebChromeClient(new WebChromeClient() {
            @Override
            public void onProgressChanged(WebView view, int newProgress) {
                progressBar.setProgress(newProgress);
            }
        });

        // Load trang Home mặc định
        if (isNetworkAvailable()) {
            webView.loadUrl(HOME_URL);
        } else {
            showError("Không có kết nối internet. Vui lòng kiểm tra lại.");
        }
    }

    private void updateAdVisibility() {
        runOnUiThread(() -> {
            boolean isConnected = isNetworkAvailable();
            if (!isConnected) {
                // Khi không có kết nối, ẩn toàn bộ container
                if (adContainer != null) {
                    adContainer.setVisibility(View.GONE);
                }
            } else {
                // Khi có kết nối, thử load quảng cáo
                loadAd();
            }
        });
    }

    private void loadAd() {
        if (isNetworkAvailable() && adView != null) {
            // Build ad request with proper configuration
            AdRequest adRequest = new AdRequest.Builder()
                .build();

            adView.loadAd(adRequest);

            // Add comprehensive ad listener for better compliance
            adView.setAdListener(new com.google.android.gms.ads.AdListener() {
                @Override
                public void onAdFailedToLoad(com.google.android.gms.ads.LoadAdError adError) {
                    super.onAdFailedToLoad(adError);
                    // Hide container when ad fails to load
                    if (adContainer != null) {
                        adContainer.setVisibility(View.GONE);
                    }
                }

                @Override
                public void onAdLoaded() {
                    super.onAdLoaded();
                    // Show container when ad loads successfully
                    if (adContainer != null) {
                        adContainer.setVisibility(View.VISIBLE);
                    }
                }

                @Override
                public void onAdOpened() {
                    super.onAdOpened();
                }

                @Override
                public void onAdClosed() {
                    super.onAdClosed();
                }

                @Override
                public void onAdClicked() {
                    super.onAdClicked();
                }
            });
        } else {
            if (adContainer != null) {
                adContainer.setVisibility(View.GONE);
            }
        }
    }

    private void setupEventListeners() {
        // Setup search functionality
        btnSearch.setOnClickListener(v -> toggleSearchView());

        searchView.setOnQueryTextListener(new SearchView.OnQueryTextListener() {
            @Override
            public boolean onQueryTextSubmit(String query) {
                if (!query.isEmpty()) {
                    webView.findAllAsync(query);
                    saveSearchHistory(query);
                }
                return true;
            }

            @Override
            public boolean onQueryTextChange(String newText) {
                return false;
            }
        });

        // Setup bottom navigation
        btnHome.setOnClickListener(v -> {
            String currentUrl = webView.getUrl();
            if (HOME_URL.equals(currentUrl)) {
                webView.scrollTo(0, 0);
            } else {
                webView.loadUrl(HOME_URL);
            }
        });

        btnBookmark.setOnClickListener(v -> toggleBookmark());
        btnShare.setOnClickListener(v -> shareCurrentPage());
        btnSettings.setOnClickListener(v -> openSettings());
    }

    private void toggleSearchView() {
        if (searchView.getVisibility() == View.VISIBLE) {
            searchView.setVisibility(View.GONE);
            toolbar.setTitle(getString(R.string.app_name));
        } else {
            searchView.setVisibility(View.VISIBLE);
            searchView.requestFocus();
            toolbar.setTitle("");
        }
    }

    private void saveSearchHistory(String query) {
        // Save search query to SharedPreferences for history
        SharedPreferences.Editor editor = preferences.edit();
        String history = preferences.getString("search_history", "");
        if (!history.contains(query)) {
            history = query + ";" + history;
            // Keep only last 20 searches
            String[] searches = history.split(";");
            if (searches.length > 20) {
                StringBuilder newHistory = new StringBuilder();
                for (int i = 0; i < 20; i++) {
                    newHistory.append(searches[i]).append(";");
                }
                history = newHistory.toString();
            }
            editor.putString("search_history", history);
            editor.apply();
        }
    }

    private void toggleBookmark() {
        String currentUrl = webView.getUrl();
        if (currentUrl != null) {
            // Simple bookmark implementation
            String bookmarks = preferences.getString("bookmarks", "");
            if (bookmarks.contains(currentUrl)) {
                // Remove bookmark
                bookmarks = bookmarks.replace(currentUrl + ";", "");
                showSnackbar(getString(R.string.message_bookmark_removed));
            } else {
                // Add bookmark
                bookmarks += currentUrl + ";";
                showSnackbar(getString(R.string.message_bookmark_added));
            }
            SharedPreferences.Editor editor = preferences.edit();
            editor.putString("bookmarks", bookmarks);
            editor.apply();
        }
    }

    private void shareCurrentPage() {
        String currentUrl = webView.getUrl();
        if (currentUrl != null) {
            Intent shareIntent = new Intent(Intent.ACTION_SEND);
            shareIntent.setType("text/plain");
            shareIntent.putExtra(Intent.EXTRA_TEXT,
                getString(R.string.share_article_text) + "\n" + currentUrl);
            startActivity(Intent.createChooser(shareIntent, getString(R.string.action_share)));
            showSnackbar(getString(R.string.message_shared));
        }
    }

    private void openSettings() {
        // Open navigation drawer to settings section
        drawerLayout.openDrawer(GravityCompat.START);
    }

    private void showSnackbar(String message) {
        Snackbar.make(findViewById(android.R.id.content), message, Snackbar.LENGTH_SHORT).show();
    }

    private boolean isNetworkAvailable() {
        ConnectivityManager connectivityManager = (ConnectivityManager) getSystemService(Context.CONNECTIVITY_SERVICE);
        if (connectivityManager != null) {
            NetworkInfo activeNetworkInfo = connectivityManager.getActiveNetworkInfo();
            return activeNetworkInfo != null && activeNetworkInfo.isConnected();
        }
        return false;
    }

    private void showError(String message) {
        Toast.makeText(this, message, Toast.LENGTH_LONG).show();
    }

    @Override
    public boolean onNavigationItemSelected(@NonNull MenuItem item) {
        int id = item.getItemId();

        if (id == R.id.nav_home) {
            webView.loadUrl(HOME_URL);
        } else if (id == R.id.nav_chapters) {
            Intent intent = new Intent(this, ChaptersActivity.class);
            startActivityForResult(intent, 100);
        } else if (id == R.id.nav_bookmarks) {
            // TODO: Implement bookmarks list
            showSnackbar("Danh sách đã lưu - Sẽ được cập nhật");
        } else if (id == R.id.nav_document_info) {
            Intent intent = new Intent(this, DocumentInfoActivity.class);
            startActivity(intent);
        } else if (id == R.id.nav_text_size) {
            showTextSizeDialog();
        } else if (id == R.id.nav_share_app) {
            shareApp();
        } else if (id == R.id.nav_privacy) {
            webView.loadUrl("https://sites.google.com/view/csbmi/home");
        }

        drawerLayout.closeDrawer(GravityCompat.START);
        return true;
    }

    private void showTextSizeDialog() {
        String[] textSizes = {"Nhỏ", "Bình thường", "Lớn", "Rất lớn"};
        int[] textSizeValues = {75, 100, 125, 150}; // Percentage values for WebView

        // Get current text size from preferences
        int currentTextSize = preferences.getInt("web_text_size", 100);
        int currentSelection = 1; // Default to "Bình thường"

        // Find current selection
        for (int i = 0; i < textSizeValues.length; i++) {
            if (textSizeValues[i] == currentTextSize) {
                currentSelection = i;
                break;
            }
        }

        // Create simple dialog using Toast for now (since AlertDialog has import issues)
        showSnackbar("Kích thước chữ: " + textSizes[currentSelection]);

        // Cycle through text sizes on each tap
        int nextIndex = (currentSelection + 1) % textSizes.length;
        int newTextSize = textSizeValues[nextIndex];

        // Save to preferences
        SharedPreferences.Editor editor = preferences.edit();
        editor.putInt("web_text_size", newTextSize);
        editor.apply();

        // Apply to WebView
        if (webView != null) {
            WebSettings settings = webView.getSettings();
            settings.setTextZoom(newTextSize);
        }

        showSnackbar("Đã thay đổi kích thước chữ: " + textSizes[nextIndex]);
    }

    private void shareApp() {
        Intent shareIntent = new Intent(Intent.ACTION_SEND);
        shareIntent.setType("text/plain");
        shareIntent.putExtra(Intent.EXTRA_TEXT, getString(R.string.share_app_text));
        startActivity(Intent.createChooser(shareIntent, getString(R.string.menu_share_app)));
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == 100 && resultCode == RESULT_OK && data != null) {
            String anchor = data.getStringExtra("chapter_anchor");
            if (anchor != null) {
                webView.loadUrl(HOME_URL + anchor);
            }
        }
    }

    @Override
    public void onBackPressed() {
        if (drawerLayout.isDrawerOpen(GravityCompat.START)) {
            drawerLayout.closeDrawer(GravityCompat.START);
        } else if (searchView.getVisibility() == View.VISIBLE) {
            toggleSearchView();
        } else if (webView.canGoBack()) {
            webView.goBack();
        } else {
            super.onBackPressed();
        }
    }

    @Override
    protected void onResume() {
        super.onResume();
        updateAdVisibility();

        // Đảm bảo thanh điều hướng hiển thị khi resume
        getWindow().getDecorView().setSystemUiVisibility(
            View.SYSTEM_UI_FLAG_LAYOUT_STABLE |
            View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
        );
    }

    @Override
    protected void onDestroy() {
        if (connectivityReceiver != null) {
            unregisterReceiver(connectivityReceiver);
        }
        super.onDestroy();
    }

    @Override
    public void onConfigurationChanged(android.content.res.Configuration newConfig) {
        super.onConfigurationChanged(newConfig);
        // Cập nhật lại kích thước quảng cáo khi xoay màn hình
        if (adView != null) {
            loadAd();
        }
    }
}

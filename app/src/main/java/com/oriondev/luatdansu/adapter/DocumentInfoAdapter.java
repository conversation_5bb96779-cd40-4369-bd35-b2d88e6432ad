package com.oriondev.luatdansu.adapter;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.google.android.material.card.MaterialCardView;
import com.oriondev.luatdansu.R;
import com.oriondev.luatdansu.model.DocumentInfo;

import java.util.ArrayList;
import java.util.List;

public class DocumentInfoAdapter extends RecyclerView.Adapter<RecyclerView.ViewHolder> {

    private static final int TYPE_HEADER = 1;
    private static final int TYPE_INFO_ITEM = 2;
    private static final int TYPE_SUMMARY = 3;
    private static final int TYPE_STATISTICS = 4;

    private Context context;
    private DocumentInfo documentInfo;
    private List<InfoItem> infoItems = new ArrayList<>();

    public DocumentInfoAdapter(Context context) {
        this.context = context;
    }

    public void setDocumentInfo(DocumentInfo documentInfo) {
        this.documentInfo = documentInfo;
        prepareInfoItems();
        notifyDataSetChanged();
    }

    private void prepareInfoItems() {
        infoItems.clear();

        if (documentInfo == null) return;

        // Add header
        infoItems.add(new InfoItem(TYPE_HEADER, "Thông tin văn bản", ""));

        // Add document details
        infoItems.add(new InfoItem(TYPE_INFO_ITEM, "Tên văn bản", documentInfo.getDocumentTitle()));
        infoItems.add(new InfoItem(TYPE_INFO_ITEM, "Số hiệu", documentInfo.getDocumentNumber()));
        infoItems.add(new InfoItem(TYPE_INFO_ITEM, "Cơ quan ban hành", documentInfo.getIssuingAuthority()));
        infoItems.add(new InfoItem(TYPE_INFO_ITEM, "Ngày ban hành", documentInfo.getIssueDate()));

        // Add summary
        infoItems.add(new InfoItem(TYPE_SUMMARY, "Tóm tắt nội dung", documentInfo.getSummary()));

        // Add statistics
        infoItems.add(new InfoItem(TYPE_STATISTICS, "Thống kê cấu trúc", ""));
    }

    @Override
    public int getItemViewType(int position) {
        return infoItems.get(position).type;
    }

    @NonNull
    @Override
    public RecyclerView.ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        LayoutInflater inflater = LayoutInflater.from(parent.getContext());

        switch (viewType) {
            case TYPE_HEADER:
                return new HeaderViewHolder(inflater.inflate(R.layout.item_document_info_header, parent, false));
            case TYPE_INFO_ITEM:
                return new InfoItemViewHolder(inflater.inflate(R.layout.item_document_info_item, parent, false));
            case TYPE_SUMMARY:
                return new SummaryViewHolder(inflater.inflate(R.layout.item_document_info_summary, parent, false));
            case TYPE_STATISTICS:
                return new StatisticsViewHolder(inflater.inflate(R.layout.item_document_info_statistics, parent, false));
            default:
                return new InfoItemViewHolder(inflater.inflate(R.layout.item_document_info_item, parent, false));
        }
    }

    @Override
    public void onBindViewHolder(@NonNull RecyclerView.ViewHolder holder, int position) {
        InfoItem item = infoItems.get(position);

        switch (holder.getItemViewType()) {
            case TYPE_HEADER:
                ((HeaderViewHolder) holder).bind(item);
                break;
            case TYPE_INFO_ITEM:
                ((InfoItemViewHolder) holder).bind(item);
                break;
            case TYPE_SUMMARY:
                ((SummaryViewHolder) holder).bind(item);
                break;
            case TYPE_STATISTICS:
                ((StatisticsViewHolder) holder).bind(item);
                break;
        }
    }

    @Override
    public int getItemCount() {
        return infoItems.size();
    }

    // ViewHolder for Header
    class HeaderViewHolder extends RecyclerView.ViewHolder {
        private TextView titleText;

        public HeaderViewHolder(@NonNull View itemView) {
            super(itemView);
            titleText = itemView.findViewById(R.id.title_text);
        }

        public void bind(InfoItem item) {
            titleText.setText(item.title);
        }
    }

    // ViewHolder for Info Item
    class InfoItemViewHolder extends RecyclerView.ViewHolder {
        private MaterialCardView cardView;
        private TextView labelText;
        private TextView valueText;

        public InfoItemViewHolder(@NonNull View itemView) {
            super(itemView);
            cardView = itemView.findViewById(R.id.card_view);
            labelText = itemView.findViewById(R.id.label_text);
            valueText = itemView.findViewById(R.id.value_text);
        }

        public void bind(InfoItem item) {
            labelText.setText(item.title);
            valueText.setText(item.content);
        }
    }

    // ViewHolder for Summary
    class SummaryViewHolder extends RecyclerView.ViewHolder {
        private MaterialCardView cardView;
        private TextView titleText;
        private TextView contentText;

        public SummaryViewHolder(@NonNull View itemView) {
            super(itemView);
            cardView = itemView.findViewById(R.id.card_view);
            titleText = itemView.findViewById(R.id.title_text);
            contentText = itemView.findViewById(R.id.content_text);
        }

        public void bind(InfoItem item) {
            titleText.setText(item.title);
            contentText.setText(item.content);
        }
    }

    // ViewHolder for Statistics
    class StatisticsViewHolder extends RecyclerView.ViewHolder {
        private MaterialCardView cardView;
        private TextView titleText;
        private TextView chapterCountText;
        private TextView articleCountText;
        private TextView clauseCountText;
        private TextView totalItemsText;

        public StatisticsViewHolder(@NonNull View itemView) {
            super(itemView);
            cardView = itemView.findViewById(R.id.card_view);
            titleText = itemView.findViewById(R.id.title_text);
            chapterCountText = itemView.findViewById(R.id.chapter_count_text);
            articleCountText = itemView.findViewById(R.id.article_count_text);
            clauseCountText = itemView.findViewById(R.id.clause_count_text);
            totalItemsText = itemView.findViewById(R.id.total_items_text);
        }

        public void bind(InfoItem item) {
            titleText.setText(item.title);

            if (documentInfo != null) {
                chapterCountText.setText(String.format("Số chương chỉ mục: %d", documentInfo.getChapterIndexCount()));
                articleCountText.setText(String.format("Số điều khoản: %d", documentInfo.getArticleCount()));
                clauseCountText.setText(String.format("Số khoản: %d", documentInfo.getClauseCount()));
                totalItemsText.setText(String.format("Tổng số mục: %d", documentInfo.getTotalItems()));
            }
        }
    }

    // Helper class for info items
    private static class InfoItem {
        int type;
        String title;
        String content;

        InfoItem(int type, String title, String content) {
            this.type = type;
            this.title = title;
            this.content = content;
        }
    }
}

package com.oriondev.luatdansu.adapter;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageButton;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.google.android.material.card.MaterialCardView;
import com.oriondev.luatdansu.R;
import com.oriondev.luatdansu.database.entity.BookmarkEntity;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Locale;

public class BookmarkAdapter extends RecyclerView.Adapter<BookmarkAdapter.BookmarkViewHolder> {

    private List<BookmarkEntity> bookmarks = new ArrayList<>();
    private OnBookmarkClickListener listener;
    private Context context;
    private SimpleDateFormat dateFormat;

    public interface OnBookmarkClickListener {
        void onBookmarkClick(BookmarkEntity bookmark);
        void onBookmarkDelete(BookmarkEntity bookmark);
        void onBookmarkEdit(BookmarkEntity bookmark);
    }

    public BookmarkAdapter(Context context, OnBookmarkClickListener listener) {
        this.context = context;
        this.listener = listener;
        this.dateFormat = new SimpleDateFormat("dd/MM/yyyy HH:mm", Locale.getDefault());
    }

    @NonNull
    @Override
    public BookmarkViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext())
                .inflate(R.layout.item_bookmark, parent, false);
        return new BookmarkViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull BookmarkViewHolder holder, int position) {
        BookmarkEntity bookmark = bookmarks.get(position);
        holder.bind(bookmark);
    }

    @Override
    public int getItemCount() {
        return bookmarks.size();
    }

    public void setBookmarks(List<BookmarkEntity> bookmarks) {
        this.bookmarks = bookmarks != null ? bookmarks : new ArrayList<>();
        notifyDataSetChanged();
    }

    class BookmarkViewHolder extends RecyclerView.ViewHolder {
        private MaterialCardView cardView;
        private TextView titleText;
        private TextView contentText;
        private TextView locationText;
        private TextView dateText;
        private TextView noteText;
        private ImageButton deleteButton;
        private ImageButton editButton;

        public BookmarkViewHolder(@NonNull View itemView) {
            super(itemView);
            cardView = itemView.findViewById(R.id.card_view);
            titleText = itemView.findViewById(R.id.title_text);
            contentText = itemView.findViewById(R.id.content_text);
            locationText = itemView.findViewById(R.id.location_text);
            dateText = itemView.findViewById(R.id.date_text);
            noteText = itemView.findViewById(R.id.note_text);
            deleteButton = itemView.findViewById(R.id.delete_button);
            editButton = itemView.findViewById(R.id.edit_button);
        }

        public void bind(BookmarkEntity bookmark) {
            // Set title
            if (bookmark.getTitle() != null && !bookmark.getTitle().isEmpty()) {
                titleText.setText(bookmark.getTitle());
                titleText.setVisibility(View.VISIBLE);
            } else {
                titleText.setVisibility(View.GONE);
            }

            // Set content preview
            if (bookmark.getContentPreview() != null && !bookmark.getContentPreview().isEmpty()) {
                contentText.setText(bookmark.getContentPreview());
                contentText.setVisibility(View.VISIBLE);
            } else {
                contentText.setVisibility(View.GONE);
            }

            // Set location (chapter and article)
            String location = bookmark.getLocationText();
            if (location != null && !location.isEmpty()) {
                locationText.setText(location);
                locationText.setVisibility(View.VISIBLE);
            } else {
                locationText.setVisibility(View.GONE);
            }

            // Set date
            Date date = new Date(bookmark.getCreatedAt());
            dateText.setText(dateFormat.format(date));

            // Set note
            if (bookmark.getNote() != null && !bookmark.getNote().isEmpty()) {
                noteText.setText(bookmark.getNote());
                noteText.setVisibility(View.VISIBLE);
            } else {
                noteText.setVisibility(View.GONE);
            }

            // Set click listeners
            cardView.setOnClickListener(v -> {
                if (listener != null) {
                    listener.onBookmarkClick(bookmark);
                }
            });

            deleteButton.setOnClickListener(v -> {
                if (listener != null) {
                    listener.onBookmarkDelete(bookmark);
                }
            });

            editButton.setOnClickListener(v -> {
                if (listener != null) {
                    listener.onBookmarkEdit(bookmark);
                }
            });
        }
    }
}

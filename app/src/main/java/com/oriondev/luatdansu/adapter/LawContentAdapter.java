package com.oriondev.luatdansu.adapter;

import android.content.Context;
import android.graphics.Typeface;
import android.text.Spannable;
import android.text.SpannableString;
import android.text.Spanned;
import android.text.style.BackgroundColorSpan;
import android.text.style.ForegroundColorSpan;
import android.text.style.StyleSpan;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.RecyclerView;

import com.google.android.gms.ads.nativead.NativeAd;
import com.google.android.gms.ads.nativead.NativeAdView;
import com.google.android.material.card.MaterialCardView;
import com.oriondev.luatdansu.R;

import com.oriondev.luatdansu.database.entity.LawEntity;

import java.util.ArrayList;
import java.util.List;

public class LawContentAdapter extends RecyclerView.Adapter<RecyclerView.ViewHolder> {
    private static final int TYPE_CHAPTER = 1;
    private static final int TYPE_ARTICLE = 2;
    private static final int TYPE_CLAUSE = 3;
    private static final int TYPE_CONTENT = 4;
    private static final int TYPE_NATIVE_AD = 5;
    
    private static final int NATIVE_AD_INTERVAL = 10; // Show native ad every 10 items

    private List<LawEntity> lawItems = new ArrayList<>();
    private List<Object> displayItems = new ArrayList<>(); // Mixed content and ads
    private List<NativeAd> nativeAds = new ArrayList<>();
    private Context context;
    private OnItemClickListener listener;
    private String searchQuery = "";

    private float textSize = 14f; // Default text size

    public interface OnItemClickListener {
        void onItemClick(LawEntity lawItem);
        void onItemLongClick(LawEntity lawItem);
    }

    public LawContentAdapter(Context context, OnItemClickListener listener) {
        this.context = context;
        this.listener = listener;
        this.nativeAds = new ArrayList<>();
        this.displayItems = new ArrayList<>();
    }

    @Override
    public int getItemViewType(int position) {
        Object item = displayItems.get(position);
        
        if (item instanceof NativeAd) {
            return TYPE_NATIVE_AD;
        }
        
        LawEntity lawItem = (LawEntity) item;
        switch (lawItem.getType()) {
            case "chapter":
                return TYPE_CHAPTER;
            case "article":
                return TYPE_ARTICLE;
            case "clause":
                return TYPE_CLAUSE;
            default:
                return TYPE_CONTENT;
        }
    }

    @NonNull
    @Override
    public RecyclerView.ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        LayoutInflater inflater = LayoutInflater.from(parent.getContext());

        switch (viewType) {
            case TYPE_CHAPTER:
                return new ChapterViewHolder(inflater.inflate(R.layout.item_chapter, parent, false));
            case TYPE_ARTICLE:
                return new ArticleViewHolder(inflater.inflate(R.layout.item_law_article, parent, false));
            case TYPE_CLAUSE:
                return new ClauseViewHolder(inflater.inflate(R.layout.item_law_clause, parent, false));
            case TYPE_NATIVE_AD:
                // Native ads disabled
                return new ContentViewHolder(inflater.inflate(R.layout.item_law_content, parent, false));
            default:
                return new ContentViewHolder(inflater.inflate(R.layout.item_law_content, parent, false));
        }
    }

    @Override
    public void onBindViewHolder(@NonNull RecyclerView.ViewHolder holder, int position) {
        Object item = displayItems.get(position);
        
        if (item instanceof NativeAd) {
            // Native ads disabled
            return;
        }
        
        LawEntity lawItem = (LawEntity) item;

        switch (holder.getItemViewType()) {
            case TYPE_CHAPTER:
                ((ChapterViewHolder) holder).bind(lawItem);
                break;
            case TYPE_ARTICLE:
                ((ArticleViewHolder) holder).bind(lawItem);
                break;
            case TYPE_CLAUSE:
                ((ClauseViewHolder) holder).bind(lawItem);
                break;
            case TYPE_CONTENT:
                ((ContentViewHolder) holder).bind(lawItem);
                break;
        }
    }

    @Override
    public int getItemCount() {
        return displayItems.size();
    }

    public void setLawItems(List<LawEntity> lawItems) {
        this.lawItems = lawItems != null ? lawItems : new ArrayList<>();
        updateDisplayItems();
        notifyDataSetChanged();
    }
    
    private void loadNativeAds() {
        // Native ads disabled for now
    }
    
    private void updateDisplayItems() {
        displayItems.clear();
        
        for (int i = 0; i < lawItems.size(); i++) {
            displayItems.add(lawItems.get(i));
            
            // Insert native ad every NATIVE_AD_INTERVAL items
            if ((i + 1) % NATIVE_AD_INTERVAL == 0 && !nativeAds.isEmpty()) {
                int adIndex = ((i + 1) / NATIVE_AD_INTERVAL - 1) % nativeAds.size();
                displayItems.add(nativeAds.get(adIndex));
            }
        }
    }

    public void setSearchQuery(String query) {
        this.searchQuery = query != null ? query : "";
        notifyDataSetChanged();
    }

    public void setTextSize(float textSize) {
        this.textSize = textSize;
        notifyDataSetChanged();
    }

    // ViewHolder for Chapter
    class ChapterViewHolder extends RecyclerView.ViewHolder {
        private MaterialCardView cardView;
        private TextView titleText;
        private TextView descriptionText; // Use description_text instead of number_text for item_chapter.xml

        public ChapterViewHolder(@NonNull View itemView) {
            super(itemView);
            cardView = itemView.findViewById(R.id.card_view);
            titleText = itemView.findViewById(R.id.title_text);
            descriptionText = itemView.findViewById(R.id.description_text);
        }

        public void bind(LawEntity item) {
            // Set title with chapter number
            String title = "";
            if (item.getNumber() != null) {
                title = "Chương " + item.getNumber();
            }
            titleText.setText(highlightSearchQuery(title));
            titleText.setTextSize(textSize);

            // Set description
            String description = item.getTitle() != null ? item.getTitle() : item.getContent();
            if (description != null) {
                descriptionText.setText(highlightSearchQuery(description));
                descriptionText.setTextSize(textSize);
                descriptionText.setVisibility(View.VISIBLE);
            } else {
                descriptionText.setVisibility(View.GONE);
            }

            cardView.setOnClickListener(v -> {
                if (listener != null) {
                    listener.onItemClick(item);
                }
            });

            cardView.setOnLongClickListener(v -> {
                if (listener != null) {
                    listener.onItemLongClick(item);
                }
                return true;
            });
        }
    }

    // ViewHolder for Article
    class ArticleViewHolder extends RecyclerView.ViewHolder {
        private MaterialCardView cardView;
        private TextView titleText;
        private TextView numberText;
        private TextView contentText;

        public ArticleViewHolder(@NonNull View itemView) {
            super(itemView);
            cardView = itemView.findViewById(R.id.card_view);
            titleText = itemView.findViewById(R.id.title_text);
            numberText = itemView.findViewById(R.id.number_text);
            contentText = itemView.findViewById(R.id.content_text);
        }

        public void bind(LawEntity item) {
            String content = item.getContent();
            String title = item.getTitle();
            String number = item.getNumber();

            // Kiểm tra xem content đã chứa "Điều X." chưa
            boolean contentHasArticleNumber = false;
            if (content != null && number != null) {
                String articlePrefix = "Điều " + number + ".";
                contentHasArticleNumber = content.trim().startsWith(articlePrefix);
            }

            // Chỉ hiển thị number nếu content chưa có
            if (number != null && !contentHasArticleNumber) {
                numberText.setText("Điều " + number);
                numberText.setVisibility(View.VISIBLE);
            } else {
                numberText.setVisibility(View.GONE);
            }

            // Xử lý thông minh để tránh trùng lặp title và content
            if (title != null && content != null) {
                if (title.equals(content) || content.contains(title) || contentHasArticleNumber) {
                    // Chỉ hiển thị content, ẩn title
                    titleText.setVisibility(View.GONE);
                    contentText.setText(highlightSearchQuery(content));
                    contentText.setVisibility(View.VISIBLE);
                } else {
                    // Hiển thị cả title và content nếu khác nhau
                    titleText.setText(highlightSearchQuery(title));
                    titleText.setVisibility(View.VISIBLE);
                    contentText.setText(highlightSearchQuery(content));
                    contentText.setVisibility(View.VISIBLE);
                }
            } else if (content != null && !content.isEmpty()) {
                // Chỉ có content
                titleText.setVisibility(View.GONE);
                contentText.setText(highlightSearchQuery(content));
                contentText.setVisibility(View.VISIBLE);
            } else if (title != null && !title.isEmpty()) {
                // Chỉ có title
                titleText.setText(highlightSearchQuery(title));
                titleText.setVisibility(View.VISIBLE);
                contentText.setVisibility(View.GONE);
            } else {
                // Không có gì
                titleText.setVisibility(View.GONE);
                contentText.setVisibility(View.GONE);
            }

            cardView.setOnClickListener(v -> {
                if (listener != null) {
                    listener.onItemClick(item);
                }
            });

            cardView.setOnLongClickListener(v -> {
                if (listener != null) {
                    listener.onItemLongClick(item);
                }
                return true;
            });
        }
    }

    // ViewHolder for Clause
    class ClauseViewHolder extends RecyclerView.ViewHolder {
        private TextView numberText;
        private TextView contentText;

        public ClauseViewHolder(@NonNull View itemView) {
            super(itemView);
            numberText = itemView.findViewById(R.id.number_text);
            contentText = itemView.findViewById(R.id.content_text);
        }

        public void bind(LawEntity item) {
            String content = item.getContent();
            String number = item.getNumber();

            // Luôn ẩn number_text để tránh trùng lặp
            // Vì content đã chứa đầy đủ thông tin bao gồm cả số thứ tự
            numberText.setVisibility(View.GONE);

            if (content != null) {
                contentText.setText(highlightSearchQuery(content));
            }

            itemView.setOnClickListener(v -> {
                if (listener != null) {
                    listener.onItemClick(item);
                }
            });

            itemView.setOnLongClickListener(v -> {
                if (listener != null) {
                    listener.onItemLongClick(item);
                }
                return true;
            });
        }
    }

    // ViewHolder for Content
    class ContentViewHolder extends RecyclerView.ViewHolder {
        private TextView contentText;

        public ContentViewHolder(@NonNull View itemView) {
            super(itemView);
            contentText = itemView.findViewById(R.id.content_text);
        }

        public void bind(LawEntity item) {
            contentText.setText(highlightSearchQuery(item.getContent()));

            itemView.setOnClickListener(v -> {
                if (listener != null) {
                    listener.onItemClick(item);
                }
            });

            itemView.setOnLongClickListener(v -> {
                if (listener != null) {
                    listener.onItemLongClick(item);
                }
                return true;
            });
        }
    }

    // Helper method to highlight search query in text
    private SpannableString highlightSearchQuery(String text) {
        if (text == null) return new SpannableString("");

        SpannableString spannableString = new SpannableString(text);

        if (!searchQuery.isEmpty()) {
            String lowerText = text.toLowerCase();
            String lowerQuery = searchQuery.toLowerCase();
            int index = lowerText.indexOf(lowerQuery);

            while (index >= 0) {
                // Sử dụng BackgroundColorSpan với màu nổi bật
                spannableString.setSpan(
                    new android.text.style.BackgroundColorSpan(
                        context.getResources().getColor(R.color.highlight_background, null)
                    ),
                    index,
                    index + searchQuery.length(),
                    Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
                );

                // Thêm StyleSpan để làm đậm
                spannableString.setSpan(
                    new StyleSpan(Typeface.BOLD),
                    index,
                    index + searchQuery.length(),
                    Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
                );

                // Thêm ForegroundColorSpan để đổi màu chữ
                spannableString.setSpan(
                    new android.text.style.ForegroundColorSpan(
                        context.getResources().getColor(R.color.highlight_text, null)
                    ),
                    index,
                    index + searchQuery.length(),
                    Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
                );

                index = lowerText.indexOf(lowerQuery, index + 1);
            }
        }

        return spannableString;
    }
    

}

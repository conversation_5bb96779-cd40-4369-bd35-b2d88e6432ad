package com.oriondev.luatdansu.model;

public class DocumentInfo {
    private String documentTitle;
    private String documentNumber;
    private String issuingAuthority;
    private String issueDate;
    private String summary;
    private int chapterIndexCount;
    private int articleCount;
    private int clauseCount;
    private int totalItems;

    public DocumentInfo() {
    }

    public DocumentInfo(String documentTitle, String documentNumber, String issuingAuthority,
                       String issueDate, String summary,
                       int chapterIndexCount, int articleCount, int clauseCount, int totalItems) {
        this.documentTitle = documentTitle;
        this.documentNumber = documentNumber;
        this.issuingAuthority = issuingAuthority;
        this.issueDate = issueDate;
        this.summary = summary;
        this.chapterIndexCount = chapterIndexCount;
        this.articleCount = articleCount;
        this.clauseCount = clauseCount;
        this.totalItems = totalItems;
    }

    // Getters and Setters
    public String getDocumentTitle() {
        return documentTitle;
    }

    public void setDocumentTitle(String documentTitle) {
        this.documentTitle = documentTitle;
    }

    public String getDocumentNumber() {
        return documentNumber;
    }

    public void setDocumentNumber(String documentNumber) {
        this.documentNumber = documentNumber;
    }

    public String getIssuingAuthority() {
        return issuingAuthority;
    }

    public void setIssuingAuthority(String issuingAuthority) {
        this.issuingAuthority = issuingAuthority;
    }

    public String getIssueDate() {
        return issueDate;
    }

    public void setIssueDate(String issueDate) {
        this.issueDate = issueDate;
    }



    public String getSummary() {
        return summary;
    }

    public void setSummary(String summary) {
        this.summary = summary;
    }

    public int getChapterIndexCount() {
        return chapterIndexCount;
    }

    public void setChapterIndexCount(int chapterIndexCount) {
        this.chapterIndexCount = chapterIndexCount;
    }

    public int getArticleCount() {
        return articleCount;
    }

    public void setArticleCount(int articleCount) {
        this.articleCount = articleCount;
    }

    public int getClauseCount() {
        return clauseCount;
    }

    public void setClauseCount(int clauseCount) {
        this.clauseCount = clauseCount;
    }

    public int getTotalItems() {
        return totalItems;
    }

    public void setTotalItems(int totalItems) {
        this.totalItems = totalItems;
    }

    @Override
    public String toString() {
        return "DocumentInfo{" +
                "documentTitle='" + documentTitle + '\'' +
                ", documentNumber='" + documentNumber + '\'' +
                ", issuingAuthority='" + issuingAuthority + '\'' +
                ", issueDate='" + issueDate + '\'' +
                ", summary='" + summary + '\'' +
                ", chapterIndexCount=" + chapterIndexCount +
                ", articleCount=" + articleCount +
                ", clauseCount=" + clauseCount +
                ", totalItems=" + totalItems +
                '}';
    }
}

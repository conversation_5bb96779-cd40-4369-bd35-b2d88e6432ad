package com.oriondev.luatdansu.model;

/**
 * Advanced DocumentInfo model with comprehensive document metadata
 */
public class DocumentInfo {
    private String documentName;
    private String contentSummary;
    private int chapterCount;
    private int articleCount;
    private String issuingAuthority;
    private String issueDate;
    private String effectiveDate;
    private long documentSize;
    private String documentVersion;
    private long lastUpdateTime;
    private int totalWords;
    private int totalCharacters;

    // Constructors
    public DocumentInfo() {
        this.lastUpdateTime = System.currentTimeMillis();
        this.documentVersion = "1.0";
    }

    public DocumentInfo(String documentName, String contentSummary, int chapterCount, 
                       int articleCount, String issuingAuthority, String issueDate, 
                       String effectiveDate) {
        this();
        this.documentName = documentName;
        this.contentSummary = contentSummary;
        this.chapterCount = chapterCount;
        this.articleCount = articleCount;
        this.issuingAuthority = issuingAuthority;
        this.issueDate = issueDate;
        this.effectiveDate = effectiveDate;
    }

    // Getters and Setters
    public String getDocumentName() {
        return documentName;
    }

    public void setDocumentName(String documentName) {
        this.documentName = documentName;
    }

    public String getContentSummary() {
        return contentSummary;
    }

    public void setContentSummary(String contentSummary) {
        this.contentSummary = contentSummary;
    }

    public int getChapterCount() {
        return chapterCount;
    }

    public void setChapterCount(int chapterCount) {
        this.chapterCount = chapterCount;
    }

    public int getArticleCount() {
        return articleCount;
    }

    public void setArticleCount(int articleCount) {
        this.articleCount = articleCount;
    }

    public String getIssuingAuthority() {
        return issuingAuthority;
    }

    public void setIssuingAuthority(String issuingAuthority) {
        this.issuingAuthority = issuingAuthority;
    }

    public String getIssueDate() {
        return issueDate;
    }

    public void setIssueDate(String issueDate) {
        this.issueDate = issueDate;
    }

    public String getEffectiveDate() {
        return effectiveDate;
    }

    public void setEffectiveDate(String effectiveDate) {
        this.effectiveDate = effectiveDate;
    }

    public long getDocumentSize() {
        return documentSize;
    }

    public void setDocumentSize(long documentSize) {
        this.documentSize = documentSize;
    }

    public String getDocumentVersion() {
        return documentVersion;
    }

    public void setDocumentVersion(String documentVersion) {
        this.documentVersion = documentVersion;
    }

    public long getLastUpdateTime() {
        return lastUpdateTime;
    }

    public void setLastUpdateTime(long lastUpdateTime) {
        this.lastUpdateTime = lastUpdateTime;
    }

    public int getTotalWords() {
        return totalWords;
    }

    public void setTotalWords(int totalWords) {
        this.totalWords = totalWords;
    }

    public int getTotalCharacters() {
        return totalCharacters;
    }

    public void setTotalCharacters(int totalCharacters) {
        this.totalCharacters = totalCharacters;
    }

    // Helper methods
    public String getFormattedSize() {
        if (documentSize < 1024) {
            return documentSize + " bytes";
        } else if (documentSize < 1024 * 1024) {
            return String.format("%.1f KB", documentSize / 1024.0);
        } else {
            return String.format("%.1f MB", documentSize / (1024.0 * 1024.0));
        }
    }

    public boolean isValid() {
        return documentName != null && !documentName.trim().isEmpty() &&
               chapterCount >= 0 && articleCount >= 0;
    }

    public String getSummaryText() {
        return String.format("Văn bản gồm %d chương, %d điều, được ban hành bởi %s",
                chapterCount, articleCount, issuingAuthority != null ? issuingAuthority : "N/A");
    }

    @Override
    public String toString() {
        return "DocumentInfo{" +
                "documentName='" + documentName + '\'' +
                ", chapterCount=" + chapterCount +
                ", articleCount=" + articleCount +
                ", issuingAuthority='" + issuingAuthority + '\'' +
                ", issueDate='" + issueDate + '\'' +
                ", effectiveDate='" + effectiveDate + '\'' +
                ", documentSize=" + documentSize +
                '}';
    }
}

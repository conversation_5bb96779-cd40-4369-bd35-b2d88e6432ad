package com.oriondev.luatdansu.utils;

import android.content.Context;
import android.util.Log;

import com.google.gson.Gson;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.oriondev.luatdansu.database.entity.LawEntity;

import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

public class JsonDataLoader {
    private static final String TAG = "JsonDataLoader";
    private Context context;
    private Gson gson;

    public JsonDataLoader(Context context) {
        this.context = context;
        this.gson = new Gson();
    }

    public List<LawEntity> loadLawDataFromAssets() {
        List<LawEntity> lawEntities = new ArrayList<>();
        Set<String> processedIds = new HashSet<>(); // Để tránh trùng lặp

        try {
            String jsonString = loadJsonFromAssets("data/luat_dan_su.json");
            Log.d(TAG, "JSON string length: " + jsonString.length());

            JsonObject rootObject = gson.fromJson(jsonString, JsonObject.class);
            Log.d(TAG, "Root object keys: " + rootObject.keySet());

            if (rootObject.has("structure")) {
                JsonArray structure = rootObject.getAsJsonArray("structure");
                Log.d(TAG, "Structure array size: " + structure.size());
                int[] orderCounter = {0}; // Use array to pass by reference
                parseStructure(structure, lawEntities, null, "", "", orderCounter, processedIds);
            } else {
                Log.w(TAG, "No 'structure' key found in root object");
            }

            Log.d(TAG, "Loaded " + lawEntities.size() + " law entities from JSON");

        } catch (Exception e) {
            Log.e(TAG, "Error loading law data from JSON", e);
        }

        return lawEntities;
    }

    private void parseStructure(JsonArray structureArray, List<LawEntity> lawEntities,
                               Integer parentId, String currentChapter, String currentArticle,
                               int[] orderCounter, Set<String> processedIds) {

        for (JsonElement element : structureArray) {
            JsonObject item = element.getAsJsonObject();
            String type = item.get("type").getAsString();

            // Tạo unique ID để tránh trùng lặp
            String uniqueId = createUniqueId(item, type, currentChapter, currentArticle);

            // Bỏ qua nếu đã xử lý
            if (processedIds.contains(uniqueId)) {
                Log.d(TAG, "Skipping duplicate: " + uniqueId);
                continue;
            }

            LawEntity entity = new LawEntity();
            entity.setType(type);
            entity.setParentId(parentId);
            entity.setOrderIndex(orderCounter[0]);

            // Set chapter and article numbers for context
            entity.setChapterNumber(currentChapter);
            entity.setArticleNumber(currentArticle);

            boolean shouldAdd = false;

            switch (type) {
                case "chapter":
                    shouldAdd = parseChapter(item, entity, currentChapter);
                    if (shouldAdd) {
                        currentChapter = entity.getNumber();
                    }
                    break;

                case "article":
                    shouldAdd = parseArticle(item, entity, currentChapter, currentArticle);
                    if (shouldAdd) {
                        currentArticle = entity.getNumber();
                    }
                    break;

                case "clause":
                    shouldAdd = parseClause(item, entity, currentChapter, currentArticle);
                    break;

                default:
                    // Bỏ qua các type khác
                    continue;
            }

            if (shouldAdd) {
                // Đánh dấu đã xử lý
                processedIds.add(uniqueId);

                // Add the entity to the list
                lawEntities.add(entity);
                int currentEntityId = lawEntities.size(); // Temporary ID for parent reference

                // Increment order counter after adding the entity
                orderCounter[0]++;

                // Parse children if they exist
                if (item.has("children")) {
                    JsonArray children = item.getAsJsonArray("children");
                    parseStructure(children, lawEntities, currentEntityId,
                                 entity.getChapterNumber(), entity.getArticleNumber(), orderCounter, processedIds);
                }
            }
        }
    }

    private String createUniqueId(JsonObject item, String type, String currentChapter, String currentArticle) {
        StringBuilder id = new StringBuilder();
        id.append(type).append("_");

        if (item.has("number")) {
            id.append(item.get("number").getAsString()).append("_");
        }

        id.append(currentChapter).append("_").append(currentArticle);

        // Thêm hash của content để đảm bảo unique
        if (item.has("content")) {
            String content = item.get("content").getAsString();
            id.append("_").append(content.hashCode());
        }

        return id.toString();
    }

    private boolean parseChapter(JsonObject item, LawEntity entity, String currentChapter) {
        if (item.has("number")) {
            entity.setNumber(item.get("number").getAsString());
        }

        // Ưu tiên content hơn title
        if (item.has("content")) {
            String content = item.get("content").getAsString();
            entity.setContent(content);

            // Extract title từ content
            if (content.startsWith("Chương")) {
                String[] parts = content.split(" ", 3);
                if (parts.length >= 3) {
                    entity.setTitle(parts[2]); // Lấy phần sau "Chương X"
                } else {
                    entity.setTitle(content);
                }
            } else {
                entity.setTitle(item.has("title") ? item.get("title").getAsString() : "");
            }
        } else if (item.has("title")) {
            entity.setTitle(item.get("title").getAsString());
            entity.setContent(item.get("title").getAsString());
        } else {
            return false; // Không có nội dung
        }

        entity.setChapterNumber(entity.getNumber());
        return true;
    }

    private boolean parseArticle(JsonObject item, LawEntity entity, String currentChapter, String currentArticle) {
        if (item.has("number")) {
            entity.setNumber(item.get("number").getAsString());
        }

        // Ưu tiên content hơn title để tránh trùng lặp
        if (item.has("content")) {
            String content = item.get("content").getAsString();
            entity.setContent(content);

            // Extract title từ content
            if (content.startsWith("Điều")) {
                String[] parts = content.split("\\.", 2);
                if (parts.length >= 2) {
                    entity.setTitle(parts[1].trim()); // Lấy phần sau "Điều X."
                } else {
                    entity.setTitle(content);
                }
            } else {
                entity.setTitle(item.has("title") ? item.get("title").getAsString() : "");
            }
        } else if (item.has("title")) {
            entity.setTitle(item.get("title").getAsString());
            entity.setContent(item.get("title").getAsString());
        } else {
            return false; // Không có nội dung
        }

        entity.setChapterNumber(currentChapter);
        entity.setArticleNumber(entity.getNumber());
        return true;
    }

    private boolean parseClause(JsonObject item, LawEntity entity, String currentChapter, String currentArticle) {
        if (item.has("number")) {
            entity.setNumber(item.get("number").getAsString());
        }

        if (item.has("content")) {
            String content = item.get("content").getAsString();
            entity.setContent(content);

            // Extract title từ content (bỏ số thứ tự ở đầu)
            String title = content.replaceFirst("^\\d+\\.\\s*", "");
            if (title.length() > 100) {
                title = title.substring(0, 97) + "...";
            }
            entity.setTitle(title);
        } else {
            return false; // Không có nội dung
        }

        entity.setChapterNumber(currentChapter);
        entity.setArticleNumber(currentArticle);
        return true;
    }


    private String loadJsonFromAssets(String fileName) throws IOException {
        InputStream inputStream = context.getAssets().open(fileName);
        int size = inputStream.available();
        byte[] buffer = new byte[size];
        inputStream.read(buffer);
        inputStream.close();
        return new String(buffer, StandardCharsets.UTF_8);
    }

    // Helper method to clean and format text
    private String cleanText(String text) {
        if (text == null) return null;

        return text.trim()
                  .replaceAll("\\s+", " ") // Replace multiple spaces with single space
                  .replaceAll("\\n+", "\n"); // Replace multiple newlines with single newline
    }

    // Method to validate loaded data
    public boolean validateData(List<LawEntity> entities) {
        if (entities == null || entities.isEmpty()) {
            Log.w(TAG, "No entities loaded");
            return false;
        }

        int chapterCount = 0;
        int articleCount = 0;

        for (LawEntity entity : entities) {
            if (entity.isChapter()) {
                chapterCount++;
            } else if (entity.isArticle()) {
                articleCount++;
            }
        }

        Log.d(TAG, "Validation: " + chapterCount + " chapters, " + articleCount + " articles");

        return chapterCount > 0 && articleCount > 0;
    }
}

package com.oriondev.luatdansu;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.SharedPreferences;
import android.net.ConnectivityManager;
import android.net.NetworkInfo;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.text.Editable;
import android.text.TextWatcher;
import android.util.Log;
import android.view.MenuItem;
import android.view.View;
import android.widget.Button;
import android.widget.EditText;
import android.widget.ImageButton;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.view.WindowCompat;
import androidx.lifecycle.ViewModelProvider;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.google.android.gms.ads.AdRequest;
import com.google.android.gms.ads.AdView;
import com.google.android.gms.ads.MobileAds;

import com.oriondev.luatdansu.adapter.LawContentAdapter;
import com.oriondev.luatdansu.database.entity.LawEntity;
import com.oriondev.luatdansu.database.repository.LawRepository;
import com.oriondev.luatdansu.utils.JsonDataLoader;
import com.oriondev.luatdansu.viewmodel.MainViewModel;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

public class MainActivity extends AppCompatActivity {

    private static final String TAG = "MainActivity";
    
    // UI Components - NATIVE ONLY, NO WEBVIEW
    private RecyclerView recyclerView;
    private LinearLayout searchContainer;
    private EditText searchEditText;
    private ImageButton searchButton, searchClear;
    private Button btnSearch, btnHome, btnBookmark, btnShare, btnSettings;
    private AdView adView;
    private ProgressBar progressBar;
    private LinearLayout adContainer;

    // Data and Logic - NATIVE ONLY
    private MainViewModel viewModel;
    private LawContentAdapter adapter;
    private BroadcastReceiver connectivityReceiver;
    private SharedPreferences preferences;
    private ExecutorService executor;

    // Search optimization
    private Handler searchHandler;
    private Runnable searchRunnable;
    private static final int SEARCH_DELAY_MS = 300;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        try {
            // Enable edge-to-edge display
            WindowCompat.setDecorFitsSystemWindows(getWindow(), false);

            setContentView(R.layout.activity_main);

            // Initialize AdMob SDK with error handling
            initializeAdMob();

            // Initialize components - ALL NATIVE, NO WEBVIEW
            initializeViews();
            initializeViewModel();
            setupRecyclerView();
            setupConnectivityReceiver();
            setupEventListeners();
            setupObservers();

            // Initialize SharedPreferences
            preferences = getSharedPreferences("app_settings", MODE_PRIVATE);
            executor = Executors.newFixedThreadPool(2);

            // Initialize search handler
            searchHandler = new Handler(Looper.getMainLooper());

            // Initialize database and load data
            initializeDatabase();

            // Update ad visibility
            updateAdVisibility();

        } catch (Exception e) {
            Log.e(TAG, "Error in onCreate: " + e.getMessage(), e);
            showError("Lỗi khởi tạo ứng dụng: " + e.getMessage());
        }
    }

    private void initializeAdMob() {
        try {
            MobileAds.initialize(this, initializationStatus -> {
                Log.d(TAG, "AdMob SDK initialized successfully");
            });
        } catch (Exception e) {
            Log.e(TAG, "Error initializing AdMob: " + e.getMessage(), e);
        }
    }

    private void initializeViews() {
        try {
            // Main views - NATIVE COMPONENTS ONLY
            recyclerView = findViewById(R.id.recyclerView);
            progressBar = findViewById(R.id.progressBar);

            // Use existing search components from layout
            searchEditText = findViewById(R.id.searchText);
            btnSearch = findViewById(R.id.btnSearch);
            btnHome = findViewById(R.id.btnHome);

            // Ad views - use existing AdView from layout
            adContainer = findViewById(R.id.adContainer);
            adView = findViewById(R.id.adView);
            
            // Configure AdView if found
            if (adView != null) {
                try {
                    adView.setAdUnitId(getString(R.string.admob_banner_id));
                } catch (Exception e) {
                    Log.e(TAG, "Error setting AdView unit ID: " + e.getMessage(), e);
                }
            }

            // Set initial visibility with null checks
            if (adContainer != null) {
                adContainer.setVisibility(View.GONE);
            }

            Log.d(TAG, "Views initialized successfully");
        } catch (Exception e) {
            Log.e(TAG, "Error initializing views: " + e.getMessage(), e);
            showError("Lỗi khởi tạo giao diện: " + e.getMessage());
        }
    }

    private void initializeViewModel() {
        try {
            viewModel = new ViewModelProvider(this).get(MainViewModel.class);
            Log.d(TAG, "ViewModel initialized successfully");
        } catch (Exception e) {
            Log.e(TAG, "Error initializing ViewModel: " + e.getMessage(), e);
            showError("Lỗi khởi tạo ViewModel: " + e.getMessage());
        }
    }

    private void setupRecyclerView() {
        try {
            if (recyclerView != null) {
                // NATIVE RECYCLERVIEW ADAPTER - NO WEBVIEW
                adapter = new LawContentAdapter(this, new LawContentAdapter.OnItemClickListener() {
                    @Override
                    public void onItemClick(LawEntity lawItem) {
                        handleLawItemClick(lawItem);
                    }

                    @Override
                    public void onItemLongClick(LawEntity lawItem) {
                        handleLawItemLongClick(lawItem);
                    }
                });

                recyclerView.setLayoutManager(new LinearLayoutManager(this));
                recyclerView.setAdapter(adapter);
                Log.d(TAG, "RecyclerView setup successfully");
            }
        } catch (Exception e) {
            Log.e(TAG, "Error setting up RecyclerView: " + e.getMessage(), e);
            showError("Lỗi thiết lập danh sách: " + e.getMessage());
        }
    }

    private void setupConnectivityReceiver() {
        try {
            connectivityReceiver = new BroadcastReceiver() {
                @Override
                public void onReceive(Context context, Intent intent) {
                    if (ConnectivityManager.CONNECTIVITY_ACTION.equals(intent.getAction())) {
                        updateAdVisibility();
                    }
                }
            };
            IntentFilter filter = new IntentFilter(ConnectivityManager.CONNECTIVITY_ACTION);
            registerReceiver(connectivityReceiver, filter);
            Log.d(TAG, "Connectivity receiver setup successfully");
        } catch (Exception e) {
            Log.e(TAG, "Error setting up connectivity receiver: " + e.getMessage(), e);
        }
    }

    private void setupEventListeners() {
        try {
            // NATIVE SEARCH FUNCTIONALITY - NO WEBVIEW SEARCH
            if (btnSearch != null) {
                btnSearch.setOnClickListener(v -> toggleSearchView());
            }

            // Search EditText listeners - NATIVE TEXT SEARCH
            if (searchEditText != null) {
                searchEditText.addTextChangedListener(new TextWatcher() {
                    @Override
                    public void beforeTextChanged(CharSequence s, int start, int count, int after) {}

                    @Override
                    public void onTextChanged(CharSequence s, int start, int before, int count) {
                        handleSearchTextChanged(s.toString());
                    }

                    @Override
                    public void afterTextChanged(Editable s) {}
                });
            }

            // Bottom navigation - ALL NATIVE
            if (btnHome != null) {
                btnHome.setOnClickListener(v -> handleHomeClick());
            }

            Log.d(TAG, "Event listeners setup successfully");
        } catch (Exception e) {
            Log.e(TAG, "Error setting up event listeners: " + e.getMessage(), e);
        }
    }

    private void handleSearchTextChanged(String query) {
        try {
            // Cancel previous search
            if (searchRunnable != null) {
                searchHandler.removeCallbacks(searchRunnable);
            }

            String trimmedQuery = query.trim();

            if (trimmedQuery.isEmpty()) {
                if (viewModel != null) viewModel.navigateToHome();
                if (adapter != null) adapter.setSearchQuery("");
            } else {
                // Debounce search with delay - NATIVE SEARCH
                searchRunnable = () -> {
                    if (adapter != null) adapter.setSearchQuery(trimmedQuery);
                    if (viewModel != null) viewModel.loadAllContent();
                };
                searchHandler.postDelayed(searchRunnable, SEARCH_DELAY_MS);
            }
        } catch (Exception e) {
            Log.e(TAG, "Error handling search text change: " + e.getMessage(), e);
        }
    }

    private void handleHomeClick() {
        try {
            if (viewModel != null) viewModel.navigateToHome();
            if (adapter != null) adapter.setSearchQuery("");
            if (recyclerView != null) recyclerView.scrollToPosition(0);
            Log.d(TAG, "Home click handled successfully");
        } catch (Exception e) {
            Log.e(TAG, "Error handling home click: " + e.getMessage(), e);
        }
    }

    private void toggleSearchView() {
        try {
            // Implementation for search view toggle
            Toast.makeText(this, "Search toggle", Toast.LENGTH_SHORT).show();
        } catch (Exception e) {
            Log.e(TAG, "Error toggling search view: " + e.getMessage(), e);
        }
    }

    private void setupObservers() {
        try {
            // Observe current content - NATIVE DATA BINDING
            if (viewModel != null) {
                viewModel.getCurrentContent().observe(this, lawItems -> {
                    try {
                        if (lawItems != null && adapter != null) {
                            adapter.setLawItems(lawItems);
                            if (progressBar != null) progressBar.setVisibility(View.GONE);
                        }
                    } catch (Exception e) {
                        Log.e(TAG, "Error updating content: " + e.getMessage(), e);
                    }
                });

                // Observe loading state
                viewModel.isLoading().observe(this, isLoading -> {
                    try {
                        if (progressBar != null) {
                            progressBar.setVisibility(isLoading ? View.VISIBLE : View.GONE);
                        }
                    } catch (Exception e) {
                        Log.e(TAG, "Error updating loading state: " + e.getMessage(), e);
                    }
                });

                // Observe error messages
                viewModel.getErrorMessage().observe(this, error -> {
                    try {
                        if (error != null && !error.isEmpty()) {
                            showError(error);
                            viewModel.clearError();
                        }
                    } catch (Exception e) {
                        Log.e(TAG, "Error handling error message: " + e.getMessage(), e);
                    }
                });
            }
            Log.d(TAG, "Observers setup successfully");
        } catch (Exception e) {
            Log.e(TAG, "Error setting up observers: " + e.getMessage(), e);
        }
    }

    private void initializeDatabase() {
        try {
            // NATIVE DATABASE INITIALIZATION - NO WEBVIEW
            if (progressBar != null) progressBar.setVisibility(View.VISIBLE);

            if (executor != null) {
                executor.execute(() -> {
                    try {
                        LawRepository repository = new LawRepository(this);
                        repository.checkAndInitializeDatabase(isEmpty -> {
                            if (isEmpty) {
                                loadDataFromJson();
                            } else {
                                runOnUiThread(() -> {
                                    try {
                                        if (viewModel != null) {
                                            viewModel.initializeDatabase();
                                            viewModel.loadAllContent();
                                        }
                                        if (progressBar != null) progressBar.setVisibility(View.GONE);
                                    } catch (Exception e) {
                                        Log.e(TAG, "Error in database initialization UI update: " + e.getMessage(), e);
                                    }
                                });
                            }
                        });
                    } catch (Exception e) {
                        Log.e(TAG, "Error in database initialization: " + e.getMessage(), e);
                        runOnUiThread(() -> showError("Lỗi khởi tạo cơ sở dữ liệu: " + e.getMessage()));
                    }
                });
            }
        } catch (Exception e) {
            Log.e(TAG, "Error starting database initialization: " + e.getMessage(), e);
            showError("Lỗi khởi tạo cơ sở dữ liệu: " + e.getMessage());
        }
    }

    private void loadDataFromJson() {
        try {
            // NATIVE JSON DATA LOADING - NO WEBVIEW
            if (executor != null) {
                executor.execute(() -> {
                    try {
                        JsonDataLoader loader = new JsonDataLoader(this);
                        List<LawEntity> lawEntities = loader.loadLawDataFromAssets();

                        if (loader.validateData(lawEntities)) {
                            LawRepository repository = new LawRepository(this);
                            repository.insertLawItems(lawEntities);

                            runOnUiThread(() -> {
                                try {
                                    if (viewModel != null) {
                                        viewModel.initializeDatabase();
                                        viewModel.loadAllContent();
                                    }
                                    showMessage("Đã tải dữ liệu thành công!");
                                    if (progressBar != null) progressBar.setVisibility(View.GONE);
                                } catch (Exception e) {
                                    Log.e(TAG, "Error in JSON load UI update: " + e.getMessage(), e);
                                }
                            });
                        } else {
                            runOnUiThread(() -> {
                                try {
                                    if (viewModel != null) {
                                        viewModel.setError("Không thể tải dữ liệu từ file JSON");
                                    }
                                    if (progressBar != null) progressBar.setVisibility(View.GONE);
                                } catch (Exception e) {
                                    Log.e(TAG, "Error in JSON validation UI update: " + e.getMessage(), e);
                                }
                            });
                        }
                    } catch (Exception e) {
                        Log.e(TAG, "Error loading JSON data: " + e.getMessage(), e);
                        runOnUiThread(() -> {
                            try {
                                if (viewModel != null) {
                                    viewModel.setError("Lỗi khi tải dữ liệu: " + e.getMessage());
                                }
                                if (progressBar != null) progressBar.setVisibility(View.GONE);
                            } catch (Exception ex) {
                                Log.e(TAG, "Error in JSON error UI update: " + ex.getMessage(), ex);
                            }
                        });
                    }
                });
            }
        } catch (Exception e) {
            Log.e(TAG, "Error starting JSON data load: " + e.getMessage(), e);
            showError("Lỗi tải dữ liệu: " + e.getMessage());
        }
    }

    private void handleLawItemClick(LawEntity lawItem) {
        try {
            // NATIVE ITEM CLICK HANDLING - NO WEBVIEW NAVIGATION
            navigateToLawItem(lawItem);
        } catch (Exception e) {
            Log.e(TAG, "Error handling law item click: " + e.getMessage(), e);
        }
    }

    private void navigateToLawItem(LawEntity lawItem) {
        try {
            // NATIVE NAVIGATION - NO WEBVIEW
            if (viewModel != null && lawItem != null) {
                viewModel.setCurrentLawItem(lawItem.getId());

                if (lawItem.isChapter()) {
                    viewModel.setCurrentChapter(lawItem.getNumber());
                } else if (lawItem.isArticle()) {
                    viewModel.setCurrentArticle(lawItem.getNumber());
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "Error navigating to law item: " + e.getMessage(), e);
        }
    }

    private void handleLawItemLongClick(LawEntity lawItem) {
        try {
            // NATIVE BOOKMARK HANDLING - NO WEBVIEW
            if (viewModel != null && lawItem != null) {
                viewModel.setCurrentLawItem(lawItem.getId());
                viewModel.toggleBookmark(lawItem);
                showMessage("Đã thêm/xóa bookmark");
            }
        } catch (Exception e) {
            Log.e(TAG, "Error handling law item long click: " + e.getMessage(), e);
        }
    }

    private void updateAdVisibility() {
        try {
            runOnUiThread(() -> {
                try {
                    boolean isConnected = isNetworkAvailable();
                    if (!isConnected) {
                        if (adContainer != null) {
                            adContainer.setVisibility(View.GONE);
                        }
                    } else {
                        loadAd();
                    }
                } catch (Exception e) {
                    Log.e(TAG, "Error in ad visibility UI update: " + e.getMessage(), e);
                }
            });
        } catch (Exception e) {
            Log.e(TAG, "Error updating ad visibility: " + e.getMessage(), e);
        }
    }

    private void loadAd() {
        try {
            if (isNetworkAvailable() && adView != null) {
                AdRequest adRequest = new AdRequest.Builder().build();
                adView.loadAd(adRequest);

                adView.setAdListener(new com.google.android.gms.ads.AdListener() {
                    @Override
                    public void onAdFailedToLoad(com.google.android.gms.ads.LoadAdError adError) {
                        super.onAdFailedToLoad(adError);
                        try {
                            if (adContainer != null) {
                                adContainer.setVisibility(View.GONE);
                            }
                        } catch (Exception e) {
                            Log.e(TAG, "Error in ad failed load: " + e.getMessage(), e);
                        }
                    }

                    @Override
                    public void onAdLoaded() {
                        super.onAdLoaded();
                        try {
                            if (adContainer != null) {
                                adContainer.setVisibility(View.VISIBLE);
                            }
                        } catch (Exception e) {
                            Log.e(TAG, "Error in ad loaded: " + e.getMessage(), e);
                        }
                    }
                });
            } else {
                if (adContainer != null) {
                    adContainer.setVisibility(View.GONE);
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "Error loading ad: " + e.getMessage(), e);
        }
    }

    private boolean isNetworkAvailable() {
        try {
            ConnectivityManager connectivityManager = (ConnectivityManager) getSystemService(Context.CONNECTIVITY_SERVICE);
            if (connectivityManager != null) {
                NetworkInfo activeNetworkInfo = connectivityManager.getActiveNetworkInfo();
                return activeNetworkInfo != null && activeNetworkInfo.isConnected();
            }
            return false;
        } catch (Exception e) {
            Log.e(TAG, "Error checking network availability: " + e.getMessage(), e);
            return false;
        }
    }

    private void showError(String message) {
        try {
            Toast.makeText(this, message, Toast.LENGTH_LONG).show();
        } catch (Exception e) {
            Log.e(TAG, "Error showing error message: " + e.getMessage(), e);
        }
    }

    private void showMessage(String message) {
        try {
            Toast.makeText(this, message, Toast.LENGTH_SHORT).show();
        } catch (Exception e) {
            Log.e(TAG, "Error showing message: " + e.getMessage(), e);
        }
    }

    @Override
    public void onBackPressed() {
        try {
            // NATIVE BACK HANDLING - NO WEBVIEW BACK NAVIGATION
            super.onBackPressed();
        } catch (Exception e) {
            Log.e(TAG, "Error in back pressed: " + e.getMessage(), e);
        }
    }

    @Override
    protected void onDestroy() {
        try {
            // CLEANUP - NO WEBVIEW CLEANUP NEEDED
            if (connectivityReceiver != null) {
                unregisterReceiver(connectivityReceiver);
            }
            if (adView != null) {
                adView.destroy();
            }
            if (executor != null) {
                executor.shutdown();
            }
            super.onDestroy();
        } catch (Exception e) {
            Log.e(TAG, "Error in onDestroy: " + e.getMessage(), e);
        }
    }
}

package com.oriondev.luatdansu.viewmodel;

import android.app.Application;
import androidx.annotation.NonNull;
import androidx.lifecycle.AndroidViewModel;
import androidx.lifecycle.LiveData;
import androidx.lifecycle.MutableLiveData;
import androidx.lifecycle.Transformations;

import com.oriondev.luatdansu.database.entity.BookmarkEntity;
import com.oriondev.luatdansu.database.entity.LawEntity;
import com.oriondev.luatdansu.repository.LawRepository;

import java.util.List;

public class MainViewModel extends AndroidViewModel {
    private LawRepository repository;
    private LiveData<List<LawEntity>> allLawItems;
    private LiveData<List<LawEntity>> allChapters;
    private LiveData<List<BookmarkEntity>> allBookmarks;

    private MutableLiveData<String> currentChapter = new MutableLiveData<>();
    private MutableLiveData<String> currentArticle = new MutableLiveData<>();
    private MutableLiveData<Integer> currentLawItemId = new MutableLiveData<>();
    private MutableLiveData<Boolean> isLoading = new MutableLiveData<>(false);
    private MutableLiveData<String> errorMessage = new MutableLiveData<>();

    // LiveData for current content based on selected chapter/article
    private LiveData<List<LawEntity>> currentContent;
    private LiveData<LawEntity> currentLawItem;
    private LiveData<Integer> isCurrentItemBookmarked;

    public MainViewModel(@NonNull Application application) {
        super(application);
        repository = new LawRepository(application);

        allLawItems = repository.getAllLawItems();
        allChapters = repository.getAllChapters();
        allBookmarks = repository.getAllBookmarks();

        // Setup transformations for current content
        currentContent = Transformations.switchMap(currentChapter, chapterNumber -> {
            if (chapterNumber != null && !chapterNumber.isEmpty()) {
                return repository.getItemsByChapter(chapterNumber);
            } else {
                // Return all law items when no specific chapter is selected
                return allLawItems;
            }
        });

        currentLawItem = Transformations.switchMap(currentLawItemId, id -> {
            if (id != null && id > 0) {
                return repository.getLawItemById(id);
            } else {
                return new MutableLiveData<>(null);
            }
        });

        isCurrentItemBookmarked = Transformations.switchMap(currentLawItemId, id -> {
            if (id != null && id > 0) {
                return repository.isBookmarked(id);
            } else {
                return new MutableLiveData<>(0);
            }
        });
    }

    // Getters for LiveData
    public LiveData<List<LawEntity>> getAllLawItems() {
        return allLawItems;
    }

    public LiveData<List<LawEntity>> getAllChapters() {
        return allChapters;
    }

    public LiveData<List<BookmarkEntity>> getAllBookmarks() {
        return allBookmarks;
    }

    public LiveData<List<LawEntity>> getCurrentContent() {
        return currentContent;
    }

    public LiveData<LawEntity> getCurrentLawItem() {
        return currentLawItem;
    }

    public LiveData<Integer> isCurrentItemBookmarked() {
        return isCurrentItemBookmarked;
    }

    public LiveData<Boolean> isLoading() {
        return isLoading;
    }

    public LiveData<String> getErrorMessage() {
        return errorMessage;
    }

    public LiveData<String> getCurrentChapter() {
        return currentChapter;
    }

    public LiveData<String> getCurrentArticle() {
        return currentArticle;
    }

    // Actions
    public void setCurrentChapter(String chapterNumber) {
        currentChapter.setValue(chapterNumber);
        currentArticle.setValue(null); // Reset article when chapter changes
    }

    public void setCurrentArticle(String articleNumber) {
        currentArticle.setValue(articleNumber);
    }

    public void setCurrentLawItem(int lawItemId) {
        currentLawItemId.setValue(lawItemId);
    }

    public void navigateToHome() {
        currentChapter.setValue(null);
        currentArticle.setValue(null);
        currentLawItemId.setValue(null);
    }

    public void loadAllContent() {
        // Trigger loading all content by setting chapter to null
        currentChapter.setValue(null);
    }

    public void loadAllChapters() {
        // Chapters are automatically loaded via LiveData
        // This method can be used to trigger refresh if needed
        isLoading.setValue(false);
    }

    public void toggleBookmark(LawEntity lawItem) {
        if (lawItem == null) return;

        Integer bookmarkStatus = isCurrentItemBookmarked.getValue();
        if (bookmarkStatus != null && bookmarkStatus > 0) {
            // Remove bookmark
            repository.deleteBookmarkByLawItemId(lawItem.getId());
        } else {
            // Add bookmark
            String contentPreview = lawItem.getContent();
            if (contentPreview != null && contentPreview.length() > 100) {
                contentPreview = contentPreview.substring(0, 100) + "...";
            }

            BookmarkEntity bookmark = new BookmarkEntity(
                lawItem.getId(),
                lawItem.getDisplayTitle(),
                contentPreview,
                lawItem.getChapterNumber(),
                lawItem.getArticleNumber(),
                null // No note initially
            );
            repository.insertBookmark(bookmark);
        }
    }

    public void deleteBookmark(com.oriondev.luatdansu.database.entity.BookmarkEntity bookmark) {
        repository.deleteBookmark(bookmark);
    }

    public void initializeDatabase() {
        isLoading.setValue(true);
        repository.checkAndInitializeDatabase(isEmpty -> {
            if (isEmpty) {
                // Database is empty, need to load data from JSON
                loadDataFromJson();
            } else {
                isLoading.postValue(false);
            }
        });
    }

    private void loadDataFromJson() {
        // This method will be called from the Activity
        // since it needs Context for JsonDataLoader
        isLoading.postValue(false);
    }

    public void loadDataFromJsonWithContext(android.content.Context context) {
        isLoading.setValue(true);

        // Use executor to load data in background
        java.util.concurrent.ExecutorService executor = java.util.concurrent.Executors.newSingleThreadExecutor();
        executor.execute(() -> {
            try {
                com.oriondev.luatdansu.utils.JsonDataLoader loader =
                    new com.oriondev.luatdansu.utils.JsonDataLoader(context);
                java.util.List<com.oriondev.luatdansu.database.entity.LawEntity> lawEntities =
                    loader.loadLawDataFromAssets();

                if (loader.validateData(lawEntities)) {
                    repository.insertLawItems(lawEntities);
                    isLoading.postValue(false);
                } else {
                    setError("Không thể tải dữ liệu từ file JSON");
                    isLoading.postValue(false);
                }
            } catch (Exception e) {
                setError("Lỗi khi tải dữ liệu: " + e.getMessage());
                isLoading.postValue(false);
            }
        });
        executor.shutdown();
    }

    public void setError(String error) {
        errorMessage.setValue(error);
    }

    public void clearError() {
        errorMessage.setValue(null);
    }

    @Override
    protected void onCleared() {
        super.onCleared();
        repository.cleanup();
    }
}

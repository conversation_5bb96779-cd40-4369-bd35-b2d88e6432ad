package com.oriondev.luatdansu;

import android.app.SearchManager;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.SharedPreferences;
import android.net.ConnectivityManager;
import android.net.NetworkInfo;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.text.Editable;
import android.text.TextWatcher;
import android.view.MenuItem;
import android.view.View;
import android.view.inputmethod.EditorInfo;
import android.widget.EditText;
import android.widget.ImageButton;
import android.widget.LinearLayout;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.appcompat.app.ActionBarDrawerToggle;
import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.widget.SearchView;
import androidx.core.splashscreen.SplashScreen;
import androidx.core.view.GravityCompat;
import androidx.core.view.WindowCompat;
import androidx.drawerlayout.widget.DrawerLayout;
import androidx.lifecycle.ViewModelProvider;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.google.android.gms.ads.AdRequest;
import com.google.android.gms.ads.AdView;
import com.google.android.gms.ads.MobileAds;
import com.google.android.material.button.MaterialButton;
import com.google.android.material.card.MaterialCardView;
import com.google.android.material.floatingactionbutton.FloatingActionButton;
import com.google.android.material.navigation.NavigationView;
import com.google.android.material.progressindicator.LinearProgressIndicator;
import com.google.android.material.snackbar.Snackbar;

import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.SharedPreferences;
import android.net.Uri;
import androidx.appcompat.app.AlertDialog;
import android.net.ConnectivityManager;
import android.net.NetworkInfo;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.text.Editable;
import android.text.TextWatcher;
import android.view.MenuItem;
import android.view.View;
import android.view.inputmethod.EditorInfo;
import android.widget.EditText;
import android.widget.ImageButton;
import android.widget.LinearLayout;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.appcompat.app.ActionBarDrawerToggle;
import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.widget.SearchView;
import androidx.core.splashscreen.SplashScreen;
import androidx.core.view.GravityCompat;
import androidx.core.view.WindowCompat;
import androidx.drawerlayout.widget.DrawerLayout;
import androidx.lifecycle.ViewModelProvider;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.google.android.gms.ads.AdRequest;
import com.google.android.gms.ads.AdView;
import com.google.android.gms.ads.MobileAds;
import com.google.android.gms.ads.MobileAds;


import com.google.android.material.appbar.MaterialToolbar;
import com.google.android.material.button.MaterialButton;
import com.google.android.material.card.MaterialCardView;
import com.google.android.material.navigation.NavigationView;
import com.google.android.material.progressindicator.LinearProgressIndicator;
import com.google.android.material.snackbar.Snackbar;

import com.oriondev.luatdansu.adapter.LawContentAdapter;
import com.oriondev.luatdansu.database.entity.LawEntity;
import com.oriondev.luatdansu.repository.LawRepository;
import com.oriondev.luatdansu.utils.JsonDataLoader;
import com.oriondev.luatdansu.viewmodel.MainViewModel;

import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

public class MainActivity extends AppCompatActivity implements NavigationView.OnNavigationItemSelectedListener {

    // UI Components
    private RecyclerView recyclerView;
    private LinearLayout searchContainer;
    private androidx.appcompat.widget.SearchView searchView;
    private MaterialButton btnSearch, btnHome, btnBookmark, btnShare, btnSettings;
    private FloatingActionButton fabSearch;
    private AdView adView;
    private LinearProgressIndicator progressBar;
    private MaterialCardView adContainer;
    private MaterialCardView disclaimerBanner;
    private MaterialToolbar toolbar;
    private DrawerLayout drawerLayout;
    private NavigationView navigationView;
    private ActionBarDrawerToggle drawerToggle;

    // Data and Logic
    private MainViewModel viewModel;
    private LawContentAdapter adapter;
    private BroadcastReceiver connectivityReceiver;
    private SharedPreferences preferences;
    private ExecutorService executor;

    // Search optimization
    private Handler searchHandler;
    private Runnable searchRunnable;
    private static final int SEARCH_DELAY_MS = 300;
    
    // Interstitial ad counter
    private int navigationCounter = 0;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        // Handle the splash screen transition
        SplashScreen splashScreen = SplashScreen.installSplashScreen(this);

        super.onCreate(savedInstanceState);

        // Enable edge-to-edge display
        WindowCompat.setDecorFitsSystemWindows(getWindow(), false);

        setContentView(R.layout.activity_main);

        // Initialize AdMob SDK
        MobileAds.initialize(this, initializationStatus -> {
            // AdMob SDK initialized
        });

        // Initialize components
        initializeViews();
        initializeViewModel();
        setupToolbarAndNavigation();
        setupRecyclerView();
        setupConnectivityReceiver();
        setupEventListeners();
        setupObservers();

        // Initialize SharedPreferences
        preferences = getSharedPreferences("app_settings", MODE_PRIVATE);
        executor = Executors.newFixedThreadPool(2);

        // Initialize search handler
        searchHandler = new Handler(Looper.getMainLooper());

        // Initialize database and load data
        initializeDatabase();

        // Update ad visibility
        updateAdVisibility();
    }

    private void initializeViews() {
        // Main views
        recyclerView = findViewById(R.id.recyclerView);
        toolbar = findViewById(R.id.toolbar);
        drawerLayout = findViewById(R.id.drawer_layout);
        navigationView = findViewById(R.id.navigation_view);
        progressBar = findViewById(R.id.progressBar);

        // Search views - Updated for new layout
        searchContainer = findViewById(R.id.searchContainer);
        searchView = findViewById(R.id.search_view);

        // Bottom navigation buttons
        btnSearch = findViewById(R.id.btnSearch);
        btnHome = findViewById(R.id.btnHome);
        btnBookmark = findViewById(R.id.btnBookmark);
        btnShare = findViewById(R.id.btnShare);
        btnSettings = findViewById(R.id.btnSettings);

        // Floating Action Button
        fabSearch = findViewById(R.id.fab_search);

        // Initialize beautiful ad views
        adContainer = findViewById(R.id.adContainer);
        LinearLayout adViewContainer = findViewById(R.id.adViewContainer);

        // Create banner ad with beautiful styling
        if (adViewContainer != null) {
            adView = new AdView(this);
            adView.setAdUnitId(getString(R.string.admob_banner_id));
            adView.setAdSize(com.google.android.gms.ads.AdSize.BANNER);

            // Add AdView to beautiful container
            adViewContainer.addView(adView);
        }
        
        // Load ad with beautiful animation
        loadAd();

        // Disclaimer banner
        disclaimerBanner = findViewById(R.id.disclaimerBanner);

        // Set initial visibility
        if (adContainer != null) {
            adContainer.setVisibility(View.GONE);
        }
        if (searchContainer != null) {
            searchContainer.setVisibility(View.GONE);
        }
        if (disclaimerBanner != null) {
            disclaimerBanner.setVisibility(View.VISIBLE);
        }
    }



    private void initializeViewModel() {
        viewModel = new ViewModelProvider(this).get(MainViewModel.class);
    }

    private void setupToolbarAndNavigation() {
        // Setup toolbar
        setSupportActionBar(toolbar);

        // Setup navigation drawer
        drawerToggle = new ActionBarDrawerToggle(
            this, drawerLayout, toolbar,
            R.string.navigation_drawer_open,
            R.string.navigation_drawer_close
        );
        drawerLayout.addDrawerListener(drawerToggle);
        drawerToggle.syncState();

        // Setup navigation view
        navigationView.setNavigationItemSelectedListener(this);
    }

    private void setupRecyclerView() {
        adapter = new LawContentAdapter(this, new LawContentAdapter.OnItemClickListener() {
            @Override
            public void onItemClick(LawEntity lawItem) {
                handleLawItemClick(lawItem);
            }

            @Override
            public void onItemLongClick(LawEntity lawItem) {
                handleLawItemLongClick(lawItem);
            }
        });

        recyclerView.setLayoutManager(new LinearLayoutManager(this));
        recyclerView.setAdapter(adapter);
    }

    private void setupConnectivityReceiver() {
        connectivityReceiver = new BroadcastReceiver() {
            @Override
            public void onReceive(Context context, Intent intent) {
                if (ConnectivityManager.CONNECTIVITY_ACTION.equals(intent.getAction())) {
                    updateAdVisibility();
                }
            }
        };
        IntentFilter filter = new IntentFilter(ConnectivityManager.CONNECTIVITY_ACTION);
        registerReceiver(connectivityReceiver, filter);
    }

    private void setupEventListeners() {
        // Search functionality with new SearchView
        if (btnSearch != null) {
            btnSearch.setOnClickListener(v -> toggleSearchView());
        }

        if (fabSearch != null) {
            fabSearch.setOnClickListener(v -> toggleSearchView());
        }

        // SearchView listeners
        if (searchView != null) {
            searchView.setOnQueryTextListener(new androidx.appcompat.widget.SearchView.OnQueryTextListener() {
                @Override
                public boolean onQueryTextSubmit(String query) {
                    if (!query.trim().isEmpty()) {
                        performSearch(query.trim());
                    }
                    return true;
                }

                @Override
                public boolean onQueryTextChange(String newText) {
                    // Cancel previous search
                    if (searchRunnable != null) {
                        searchHandler.removeCallbacks(searchRunnable);
                    }

                    String query = newText.trim();

                    if (query.isEmpty()) {
                        viewModel.navigateToHome();
                        adapter.setSearchQuery("");
                    } else {
                        // Debounce search with delay
                        searchRunnable = () -> {
                            adapter.setSearchQuery(query);
                            viewModel.loadAllContent();
                        };
                        searchHandler.postDelayed(searchRunnable, SEARCH_DELAY_MS);
                    }
                    return true;
                }
            });
        }

        // Bottom navigation
        btnHome.setOnClickListener(v -> {
            viewModel.navigateToHome();
            adapter.setSearchQuery("");
            if (searchContainer.getVisibility() == View.VISIBLE) {
                toggleSearchView();
            }
            recyclerView.scrollToPosition(0);
        });

        btnBookmark.setOnClickListener(v -> toggleBookmark());
        btnShare.setOnClickListener(v -> shareCurrentContent());
        btnSettings.setOnClickListener(v -> openSettings());
    }

    private void setupObservers() {
        // Observe current content
        viewModel.getCurrentContent().observe(this, lawItems -> {
            if (lawItems != null) {
                adapter.setLawItems(lawItems);
                progressBar.setVisibility(View.GONE);
            }
        });

        // Observe loading state - DISABLED
        viewModel.isLoading().observe(this, isLoading -> {
            // Loading indicator disabled per user request
            progressBar.setVisibility(View.GONE);
        });

        // Observe error messages
        viewModel.getErrorMessage().observe(this, error -> {
            if (error != null && !error.isEmpty()) {
                showError(error);
                viewModel.clearError();
            }
        });

        // Observe current chapter
        viewModel.getCurrentChapter().observe(this, chapterNumber -> {
            updateToolbarTitle(chapterNumber);
        });
    }

    private void initializeDatabase() {
        // Progress bar disabled per user request
        progressBar.setVisibility(View.GONE);

        executor.execute(() -> {
            LawRepository repository = new LawRepository(this);
            repository.checkAndInitializeDatabase(isEmpty -> {
                if (isEmpty) {
                    loadDataFromJson();
                } else {
                    runOnUiThread(() -> {
                        viewModel.initializeDatabase();
                        viewModel.loadAllContent();
                        // Keep progress bar hidden
                        progressBar.setVisibility(View.GONE);
                    });
                }
            });
        });
    }

    private void loadDataFromJson() {
        executor.execute(() -> {
            try {
                // Test JSON loading first
                TestJsonLoader.testJsonLoading(this);

                JsonDataLoader loader = new JsonDataLoader(this);
                List<LawEntity> lawEntities = loader.loadLawDataFromAssets();

                if (loader.validateData(lawEntities)) {
                    LawRepository repository = new LawRepository(this);
                    repository.insertLawItems(lawEntities);

                    runOnUiThread(() -> {
                        viewModel.initializeDatabase();
                        viewModel.loadAllContent();
                        showSnackbar("Đã tải dữ liệu thành công!");
                    });
                } else {
                    runOnUiThread(() -> {
                        viewModel.setError("Không thể tải dữ liệu từ file JSON");
                    });
                }
            } catch (Exception e) {
                runOnUiThread(() -> {
                    viewModel.setError("Lỗi khi tải dữ liệu: " + e.getMessage());
                });
            }
        });
    }

    private void forceReloadDataFromJson() {
        runOnUiThread(() -> {
            showSnackbar("Đang tải lại dữ liệu từ JSON...");
            progressBar.setVisibility(View.VISIBLE);
        });

        executor.execute(() -> {
            try {
                LawRepository repository = new LawRepository(this);

                // Clear existing data first
                repository.deleteAllLawItems();

                // Wait a bit for deletion to complete
                Thread.sleep(500);

                // Test JSON loading first
                TestJsonLoader.testJsonLoading(this);

                JsonDataLoader loader = new JsonDataLoader(this);
                List<LawEntity> lawEntities = loader.loadLawDataFromAssets();

                if (loader.validateData(lawEntities)) {
                    repository.insertLawItems(lawEntities);

                    runOnUiThread(() -> {
                        progressBar.setVisibility(View.GONE);
                        viewModel.initializeDatabase();
                        viewModel.loadAllContent();
                        showSnackbar("✅ Đã tải lại dữ liệu thành công từ JSON!");
                    });
                } else {
                    runOnUiThread(() -> {
                        progressBar.setVisibility(View.GONE);
                        viewModel.setError("❌ Không thể tải dữ liệu từ file JSON");
                    });
                }
            } catch (Exception e) {
                runOnUiThread(() -> {
                    progressBar.setVisibility(View.GONE);
                    viewModel.setError("❌ Lỗi khi tải dữ liệu: " + e.getMessage());
                });
            }
        });
    }

    private void handleLawItemClick(LawEntity lawItem) {
        // Navigate directly
        navigateToLawItem(lawItem);
    }
    
    private void navigateToLawItem(LawEntity lawItem) {
        viewModel.setCurrentLawItem(lawItem.getId());

        if (lawItem.isChapter()) {
            viewModel.setCurrentChapter(lawItem.getNumber());
        } else if (lawItem.isArticle()) {
            viewModel.setCurrentArticle(lawItem.getNumber());
        }
    }

    private void handleLawItemLongClick(LawEntity lawItem) {
        viewModel.setCurrentLawItem(lawItem.getId());
        viewModel.toggleBookmark(lawItem);

        String message = "Đã thêm/xóa bookmark";
        showSnackbar(message);
    }

    private void performSearch(String query) {
        Intent searchIntent = new Intent(this, SearchActivity.class);
        searchIntent.setAction(Intent.ACTION_SEARCH);
        searchIntent.putExtra(SearchManager.QUERY, query);
        startActivityForResult(searchIntent, 200);
    }

    private void toggleSearchView() {
        if (searchContainer != null) {
            if (searchContainer.getVisibility() == View.VISIBLE) {
                searchContainer.setVisibility(View.GONE);
                if (searchView != null) {
                    searchView.setQuery("", false);
                    searchView.clearFocus();
                }
                toolbar.setTitle(getString(R.string.app_name));
                viewModel.navigateToHome();
                adapter.setSearchQuery("");
            } else {
                searchContainer.setVisibility(View.VISIBLE);
                if (searchView != null) {
                    searchView.requestFocus();
                }
                toolbar.setTitle("");
            }
        }
    }

    private void toggleBookmark() {
        Intent intent = new Intent(this, BookmarkActivity.class);
        startActivityForResult(intent, 300);
    }

    private void shareCurrentContent() {
        Intent shareIntent = new Intent(Intent.ACTION_SEND);
        shareIntent.setType("text/plain");
        shareIntent.putExtra(Intent.EXTRA_TEXT,
            getString(R.string.share_article_text) + "\n" + getString(R.string.app_name));
        startActivity(Intent.createChooser(shareIntent, getString(R.string.action_share)));
        showSnackbar("Đã chia sẻ");
    }

    private void openSettings() {
        drawerLayout.openDrawer(GravityCompat.START);
    }

    private void updateToolbarTitle(String chapterNumber) {
        if (chapterNumber != null && !chapterNumber.isEmpty()) {
            toolbar.setTitle("Chương " + chapterNumber);
        } else {
            toolbar.setTitle(getString(R.string.app_name));
        }
    }

    private void updateAdVisibility() {
        runOnUiThread(() -> {
            boolean isConnected = isNetworkAvailable();
            if (!isConnected) {
                if (adContainer != null) {
                    adContainer.setVisibility(View.GONE);
                }
            } else {
                loadAd();
            }
        });
    }

    private void loadAd() {
        if (isNetworkAvailable() && adView != null) {
            // Load ad directly
            AdRequest adRequest = new AdRequest.Builder().build();
            adView.loadAd(adRequest);

            // Show container with smooth animation after a delay
            showAdContainerWithAnimation();

            adView.setAdListener(new com.google.android.gms.ads.AdListener() {
                @Override
                public void onAdFailedToLoad(com.google.android.gms.ads.LoadAdError adError) {
                    super.onAdFailedToLoad(adError);
                    // Gracefully hide ad container when ad fails to load
                    runOnUiThread(() -> {
                        if (adContainer != null && adContainer.getVisibility() == View.VISIBLE) {
                            adContainer.animate()
                                .alpha(0f)
                                .setDuration(200)
                                .withEndAction(() -> adContainer.setVisibility(View.GONE))
                                .start();
                        }
                    });
                }

                @Override
                public void onAdLoaded() {
                    super.onAdLoaded();
                    // Show ad container when ad loads successfully
                    runOnUiThread(() -> {
                        if (adContainer != null) {
                            adContainer.setVisibility(View.VISIBLE);
                        }
                    });
                }

                @Override
                public void onAdOpened() {
                    super.onAdOpened();
                }

                @Override
                public void onAdClosed() {
                    super.onAdClosed();
                }

                @Override
                public void onAdClicked() {
                    super.onAdClicked();
                }
            });
        } else {
            // Hide ad container when network is not available
            if (adContainer != null) {
                adContainer.setVisibility(View.GONE);
            }
        }
    }

    private boolean isNetworkAvailable() {
        ConnectivityManager connectivityManager = (ConnectivityManager) getSystemService(Context.CONNECTIVITY_SERVICE);
        if (connectivityManager != null) {
            NetworkInfo activeNetworkInfo = connectivityManager.getActiveNetworkInfo();
            return activeNetworkInfo != null && activeNetworkInfo.isConnected();
        }
        return false;
    }

    private void showAdContainerWithAnimation() {
        if (adContainer != null && adContainer.getVisibility() != View.VISIBLE) {
            adContainer.setAlpha(0f);
            adContainer.setVisibility(View.VISIBLE);
            adContainer.animate()
                .alpha(1f)
                .setDuration(300)
                .setStartDelay(500) // Delay to let content load first
                .start();
        }
    }

    private void showSnackbar(String message) {
        Snackbar.make(findViewById(android.R.id.content), message, Snackbar.LENGTH_SHORT).show();
    }

    private void showError(String message) {
        Toast.makeText(this, message, Toast.LENGTH_LONG).show();
    }

    @Override
    public boolean onNavigationItemSelected(@NonNull MenuItem item) {
        int id = item.getItemId();

        if (id == R.id.nav_home) {
            viewModel.navigateToHome();
            adapter.setSearchQuery("");
            if (searchContainer.getVisibility() == View.VISIBLE) {
                toggleSearchView();
            }
        } else if (id == R.id.nav_chapters) {
            Intent intent = new Intent(this, ChaptersActivity.class);
            startActivityForResult(intent, 100);
        } else if (id == R.id.nav_bookmarks) {
            Intent intent = new Intent(this, BookmarkActivity.class);
            startActivity(intent);
        } else if (id == R.id.nav_document_info) {
            Intent intent = new Intent(this, DocumentInfoActivity.class);
            startActivityForResult(intent, 300);
        } else if (id == R.id.nav_text_size) {
            showTextSizeDialog();
        } else if (id == R.id.nav_reload_data) {
            forceReloadDataFromJson();
        } else if (id == R.id.nav_share_app) {
            shareApp();
        } else if (id == R.id.nav_privacy) {
            openPrivacyPolicy();
        }

        drawerLayout.closeDrawer(GravityCompat.START);
        return true;
    }

    private void showTextSizeDialog() {
        String[] textSizes = {"Nhỏ", "Bình thường", "Lớn", "Rất lớn"};
        float[] textSizeValues = {12f, 14f, 16f, 18f};

        // Get current text size from preferences
        float currentTextSize = preferences.getFloat("text_size", 14f);
        int currentSelection = 1; // Default to "Bình thường"

        // Find current selection
        for (int i = 0; i < textSizeValues.length; i++) {
            if (Math.abs(textSizeValues[i] - currentTextSize) < 0.1f) {
                currentSelection = i;
                break;
            }
        }

        // Simple implementation - cycle through sizes on each tap
        int nextIndex = (currentSelection + 1) % textSizes.length;
        float newTextSize = textSizeValues[nextIndex];

        // Save to preferences
        SharedPreferences.Editor editor = preferences.edit();
        editor.putFloat("text_size", newTextSize);
        editor.apply();

        showSnackbar("Kích thước chữ: " + textSizes[nextIndex]);
    }

    private void openPrivacyPolicy() {
        try {
            Intent intent = new Intent(Intent.ACTION_VIEW);
            intent.setData(Uri.parse("https://sites.google.com/view/csbmi/home"));
            startActivity(intent);
        } catch (Exception e) {
            showSnackbar("Không thể mở liên kết. Vui lòng thử lại.");
        }
    }

    private void shareApp() {
        Intent shareIntent = new Intent(Intent.ACTION_SEND);
        shareIntent.setType("text/plain");
        shareIntent.putExtra(Intent.EXTRA_TEXT, getString(R.string.share_app_text));
        startActivity(Intent.createChooser(shareIntent, getString(R.string.menu_share_app)));
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);

        if (resultCode == RESULT_OK && data != null) {
            if (requestCode == 100) {
                // From ChaptersActivity
                String anchor = data.getStringExtra("chapter_anchor");
                if (anchor != null) {
                    String chapterNumber = anchor.replace("#chuong_", "");
                    viewModel.setCurrentChapter(chapterNumber);
                }
            } else if (requestCode == 200) {
                // From SearchActivity
                handleSearchResult(data);
            } else if (requestCode == 300) {
                // From BookmarkActivity
                handleBookmarkResult(data);
            }
        }
    }

    private void handleSearchResult(Intent data) {
        int lawItemId = data.getIntExtra("selected_law_item_id", -1);
        String chapterNumber = data.getStringExtra("chapter_number");
        String articleNumber = data.getStringExtra("article_number");

        if (lawItemId != -1) {
            viewModel.setCurrentLawItem(lawItemId);
        }
        if (chapterNumber != null) {
            viewModel.setCurrentChapter(chapterNumber);
        }
        if (articleNumber != null) {
            viewModel.setCurrentArticle(articleNumber);
        }
    }

    private void handleBookmarkResult(Intent data) {
        int lawItemId = data.getIntExtra("selected_law_item_id", -1);
        String chapterNumber = data.getStringExtra("chapter_number");
        String articleNumber = data.getStringExtra("article_number");

        if (lawItemId != -1) {
            viewModel.setCurrentLawItem(lawItemId);
        }
        if (chapterNumber != null) {
            viewModel.setCurrentChapter(chapterNumber);
        }
        if (articleNumber != null) {
            viewModel.setCurrentArticle(articleNumber);
        }

        showSnackbar("Đã chuyển đến bookmark");
    }

    @Override
    public void onBackPressed() {
        if (drawerLayout.isDrawerOpen(GravityCompat.START)) {
            drawerLayout.closeDrawer(GravityCompat.START);
        } else if (searchContainer.getVisibility() == View.VISIBLE) {
            toggleSearchView();
        } else {
            super.onBackPressed();
        }
    }

    @Override
    protected void onResume() {
        super.onResume();
        updateAdVisibility();

        // Resume AdView
        if (adView != null) {
            adView.resume();
        }

        getWindow().getDecorView().setSystemUiVisibility(
            View.SYSTEM_UI_FLAG_LAYOUT_STABLE |
            View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
        );
    }

    @Override
    protected void onPause() {
        // Pause AdView
        if (adView != null) {
            adView.pause();
        }
        super.onPause();
    }

    @Override
    protected void onDestroy() {
        // Destroy AdView
        if (adView != null) {
            adView.destroy();
        }

        if (connectivityReceiver != null) {
            unregisterReceiver(connectivityReceiver);
        }
        if (executor != null && !executor.isShutdown()) {
            executor.shutdown();
        }
        super.onDestroy();
    }



    @Override
    public void onConfigurationChanged(android.content.res.Configuration newConfig) {
        super.onConfigurationChanged(newConfig);
        if (adView != null) {
            loadAd();
        }
    }
}

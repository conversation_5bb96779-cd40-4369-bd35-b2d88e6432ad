package com.oriondev.luatdansu;

import android.os.Bundle;
import android.util.Log;
import android.widget.TextView;
import androidx.appcompat.app.AppCompatActivity;

import com.oriondev.luatdansu.database.entity.LawEntity;
import com.oriondev.luatdansu.utils.JsonDataLoader;

import java.util.List;

public class TestDataActivity extends AppCompatActivity {
    private static final String TAG = "TestDataActivity";
    private TextView textView;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        
        textView = new TextView(this);
        textView.setPadding(16, 16, 16, 16);
        setContentView(textView);
        
        testDataLoading();
    }
    
    private void testDataLoading() {
        try {
            JsonDataLoader loader = new JsonDataLoader(this);
            List<LawEntity> entities = loader.loadLawDataFromAssets();
            
            StringBuilder sb = new StringBuilder();
            sb.append("Total entities loaded: ").append(entities.size()).append("\n\n");
            
            int chapterCount = 0;
            int articleCount = 0;
            int clauseCount = 0;
            int contentCount = 0;
            
            for (int i = 0; i < Math.min(entities.size(), 20); i++) {
                LawEntity entity = entities.get(i);
                
                switch (entity.getType()) {
                    case "chapter":
                        chapterCount++;
                        break;
                    case "article":
                        articleCount++;
                        break;
                    case "clause":
                        clauseCount++;
                        break;
                    case "content":
                        contentCount++;
                        break;
                }
                
                sb.append("Entity ").append(i + 1).append(":\n");
                sb.append("  Type: ").append(entity.getType()).append("\n");
                sb.append("  Number: ").append(entity.getNumber()).append("\n");
                sb.append("  Title: ").append(entity.getTitle()).append("\n");
                sb.append("  Content: ").append(entity.getContent() != null ? 
                    (entity.getContent().length() > 100 ? 
                        entity.getContent().substring(0, 100) + "..." : 
                        entity.getContent()) : "null").append("\n");
                sb.append("  Chapter: ").append(entity.getChapterNumber()).append("\n");
                sb.append("  Article: ").append(entity.getArticleNumber()).append("\n");
                sb.append("  Order: ").append(entity.getOrderIndex()).append("\n\n");
            }
            
            sb.append("\nSummary:\n");
            sb.append("Chapters: ").append(chapterCount).append("\n");
            sb.append("Articles: ").append(articleCount).append("\n");
            sb.append("Clauses: ").append(clauseCount).append("\n");
            sb.append("Content: ").append(contentCount).append("\n");
            
            textView.setText(sb.toString());
            
            Log.d(TAG, "Data loading test completed");
            Log.d(TAG, "Total entities: " + entities.size());
            Log.d(TAG, "Chapters: " + chapterCount + ", Articles: " + articleCount);
            
        } catch (Exception e) {
            Log.e(TAG, "Error testing data loading", e);
            textView.setText("Error: " + e.getMessage());
        }
    }
}

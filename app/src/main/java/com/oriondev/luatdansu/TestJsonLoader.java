package com.oriondev.luatdansu;

import android.content.Context;
import android.util.Log;

import com.oriondev.luatdansu.database.entity.LawEntity;
import com.oriondev.luatdansu.utils.JsonDataLoader;

import java.util.List;

public class TestJsonLoader {
    private static final String TAG = "TestJsonLoader";
    
    public static void testJsonLoading(Context context) {
        Log.d(TAG, "Starting JSON loading test...");
        
        try {
            JsonDataLoader loader = new JsonDataLoader(context);
            List<LawEntity> entities = loader.loadLawDataFromAssets();
            
            Log.d(TAG, "Total entities loaded: " + entities.size());
            
            // Count by type
            int chapterCount = 0;
            int articleCount = 0;
            int clauseCount = 0;
            
            for (LawEntity entity : entities) {
                switch (entity.getType()) {
                    case "chapter":
                        chapterCount++;
                        if (chapterCount <= 3) {
                            Log.d(TAG, "Chapter " + chapterCount + ": " + entity.getNumber() + " - " + entity.getTitle());
                        }
                        break;
                    case "article":
                        articleCount++;
                        if (articleCount <= 5) {
                            Log.d(TAG, "Article " + articleCount + ": " + entity.getNumber() + " - " + entity.getTitle());
                        }
                        break;
                    case "clause":
                        clauseCount++;
                        if (clauseCount <= 5) {
                            Log.d(TAG, "Clause " + clauseCount + ": " + entity.getNumber() + " - " + entity.getContent().substring(0, Math.min(50, entity.getContent().length())));
                        }
                        break;
                }
            }
            
            Log.d(TAG, "Summary - Chapters: " + chapterCount + ", Articles: " + articleCount + ", Clauses: " + clauseCount);
            
            // Check for duplicates
            checkForDuplicates(entities);
            
        } catch (Exception e) {
            Log.e(TAG, "Error testing JSON loading", e);
        }
    }
    
    private static void checkForDuplicates(List<LawEntity> entities) {
        Log.d(TAG, "Checking for duplicates...");
        
        // Check for duplicate articles
        for (int i = 0; i < entities.size(); i++) {
            LawEntity entity1 = entities.get(i);
            if (!"article".equals(entity1.getType())) continue;
            
            for (int j = i + 1; j < entities.size(); j++) {
                LawEntity entity2 = entities.get(j);
                if (!"article".equals(entity2.getType())) continue;
                
                if (entity1.getNumber() != null && entity1.getNumber().equals(entity2.getNumber())) {
                    Log.w(TAG, "Duplicate article found: " + entity1.getNumber());
                    Log.w(TAG, "  First: " + entity1.getTitle());
                    Log.w(TAG, "  Second: " + entity2.getTitle());
                }
            }
        }
        
        // Check for duplicate clauses in same article
        for (int i = 0; i < entities.size(); i++) {
            LawEntity entity1 = entities.get(i);
            if (!"clause".equals(entity1.getType())) continue;
            
            for (int j = i + 1; j < entities.size(); j++) {
                LawEntity entity2 = entities.get(j);
                if (!"clause".equals(entity2.getType())) continue;
                
                if (entity1.getNumber() != null && entity1.getNumber().equals(entity2.getNumber()) &&
                    entity1.getArticleNumber() != null && entity1.getArticleNumber().equals(entity2.getArticleNumber())) {
                    Log.w(TAG, "Duplicate clause found: Article " + entity1.getArticleNumber() + ", Clause " + entity1.getNumber());
                }
            }
        }
        
        Log.d(TAG, "Duplicate check completed");
    }
}

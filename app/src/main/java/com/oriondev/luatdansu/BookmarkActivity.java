package com.oriondev.luatdansu;

import android.content.Intent;
import android.os.Bundle;
import android.view.MenuItem;
import android.view.View;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;
import androidx.lifecycle.ViewModelProvider;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.google.android.material.appbar.MaterialToolbar;
import com.google.android.material.snackbar.Snackbar;

import com.oriondev.luatdansu.adapter.BookmarkAdapter;
import com.oriondev.luatdansu.database.entity.BookmarkEntity;
import com.oriondev.luatdansu.viewmodel.MainViewModel;

public class BookmarkActivity extends AppCompatActivity {

    private RecyclerView recyclerView;
    private TextView emptyStateText;
    private MaterialToolbar toolbar;

    private MainViewModel viewModel;
    private BookmarkAdapter adapter;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_bookmark);

        initializeViews();
        setupToolbar();
        setupRecyclerView();
        setupViewModel();
        setupObservers();
    }

    private void initializeViews() {
        toolbar = findViewById(R.id.toolbar);
        recyclerView = findViewById(R.id.recycler_view);
        emptyStateText = findViewById(R.id.empty_state_text);
    }

    private void setupToolbar() {
        setSupportActionBar(toolbar);
        if (getSupportActionBar() != null) {
            getSupportActionBar().setDisplayHomeAsUpEnabled(true);
            getSupportActionBar().setTitle("Đã lưu");
        }
    }

    private void setupRecyclerView() {
        adapter = new BookmarkAdapter(this, new BookmarkAdapter.OnBookmarkClickListener() {
            @Override
            public void onBookmarkClick(BookmarkEntity bookmark) {
                // Return to MainActivity with selected bookmark
                Intent resultIntent = new Intent();
                resultIntent.putExtra("selected_law_item_id", bookmark.getLawItemId());
                resultIntent.putExtra("chapter_number", bookmark.getChapterNumber());
                resultIntent.putExtra("article_number", bookmark.getArticleNumber());
                setResult(RESULT_OK, resultIntent);
                finish();
            }

            @Override
            public void onBookmarkDelete(BookmarkEntity bookmark) {
                viewModel.deleteBookmark(bookmark);
                showSnackbar("Đã xóa bookmark");
            }

            @Override
            public void onBookmarkEdit(BookmarkEntity bookmark) {
                // TODO: Implement bookmark editing (add notes, etc.)
                showSnackbar("Chỉnh sửa bookmark - Sẽ được cập nhật");
            }
        });

        recyclerView.setLayoutManager(new LinearLayoutManager(this));
        recyclerView.setAdapter(adapter);
    }

    private void setupViewModel() {
        viewModel = new ViewModelProvider(this).get(MainViewModel.class);
    }

    private void setupObservers() {
        viewModel.getAllBookmarks().observe(this, bookmarks -> {
            if (bookmarks != null && !bookmarks.isEmpty()) {
                adapter.setBookmarks(bookmarks);
                recyclerView.setVisibility(View.VISIBLE);
                emptyStateText.setVisibility(View.GONE);
            } else {
                recyclerView.setVisibility(View.GONE);
                emptyStateText.setVisibility(View.VISIBLE);
            }
        });
    }

    private void showSnackbar(String message) {
        Snackbar.make(findViewById(android.R.id.content), message, Snackbar.LENGTH_SHORT).show();
    }

    @Override
    public boolean onOptionsItemSelected(@NonNull MenuItem item) {
        if (item.getItemId() == android.R.id.home) {
            onBackPressed();
            return true;
        }
        return super.onOptionsItemSelected(item);
    }
}

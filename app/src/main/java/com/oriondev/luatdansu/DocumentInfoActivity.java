package com.oriondev.luatdansu;

import android.content.Intent;
import android.content.SharedPreferences;
import android.graphics.Color;
import android.graphics.Typeface;
import android.net.Uri;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.text.SpannableString;
import android.text.Spanned;
import android.text.style.ForegroundColorSpan;
import android.text.style.StyleSpan;
import android.util.Log;
import android.view.MenuItem;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.ScrollView;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.appcompat.app.ActionBarDrawerToggle;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.view.GravityCompat;
import androidx.drawerlayout.widget.DrawerLayout;

import com.google.android.material.appbar.MaterialToolbar;
import com.google.android.material.card.MaterialCardView;
import com.google.android.material.navigation.NavigationView;
import com.google.android.material.progressindicator.LinearProgressIndicator;
import com.oriondev.luatdansu.utils.JsonDataLoader;
import com.oriondev.luatdansu.model.DocumentInfo;
import com.oriondev.luatdansu.database.entity.LawEntity;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Locale;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

public class DocumentInfoActivity extends AppCompatActivity implements NavigationView.OnNavigationItemSelectedListener {

    private static final String TAG = "DocumentInfoActivity";
    private static final String PREFS_NAME = "DocumentInfoPrefs";
    private static final String KEY_LAST_VIEW_TIME = "last_view_time";
    private static final String KEY_VIEW_COUNT = "view_count";

    // Advanced UI Components with comprehensive functionality
    private MaterialToolbar toolbar;
    private DrawerLayout drawerLayout;
    private NavigationView navigationView;
    private ActionBarDrawerToggle drawerToggle;
    private ScrollView scrollView;
    private LinearLayout contentContainer;
    private LinearProgressIndicator progressBar;
    private MaterialCardView documentCard, statisticsCard, metadataCard;

    // Document information display components
    private TextView documentNameText, documentSummaryText, chapterCountText;
    private TextView articleCountText, issuingAuthorityText, issueDateText;
    private TextView effectiveDateText, lastUpdatedText, viewCountText;
    private TextView documentSizeText, documentVersionText;

    // Advanced Data Management and Business Logic
    private ExecutorService executor;
    private SharedPreferences preferences;
    private Handler uiHandler;
    private DocumentInfo documentInfo;
    private List<LawEntity> lawEntities;
    private SimpleDateFormat dateFormatter;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_main);

        try {
            Log.d(TAG, "Starting comprehensive DocumentInfoActivity initialization");

            // Initialize core components with advanced error handling
            initializeAdvancedViewComponents();
            initializeAdvancedDataLayer();
            setupAdvancedToolbarAndNavigation();
            setupAdvancedDocumentInfoLayout();

            // Load and display document information with comprehensive processing
            loadAdvancedDocumentInformation();

            // Track user interaction for analytics
            trackAdvancedUserInteraction();

            Log.d(TAG, "DocumentInfoActivity initialization completed successfully");

        } catch (Exception e) {
            Log.e(TAG, "Critical error during DocumentInfoActivity initialization", e);
            handleAdvancedInitializationError(e);
        }
    }

    /**
     * Initialize view components with comprehensive null checking and validation
     */
    private void initializeAdvancedViewComponents() {
        try {
            // Core navigation components
            toolbar = findViewById(R.id.toolbar);
            drawerLayout = findViewById(R.id.drawer_layout);
            navigationView = findViewById(R.id.navigation_view);

            // Main content components
            progressBar = findViewById(R.id.progressBar);

            // Create dynamic content container since it doesn't exist in layout
            contentContainer = new LinearLayout(this);
            contentContainer.setOrientation(LinearLayout.VERTICAL);
            contentContainer.setPadding(16, 16, 16, 16);

            // Find main content area and add our container
            View mainContent = findViewById(R.id.recyclerView);
            if (mainContent != null && mainContent.getParent() instanceof LinearLayout) {
                LinearLayout parent = (LinearLayout) mainContent.getParent();
                parent.addView(contentContainer, 0);
                mainContent.setVisibility(View.GONE);
            }

            // Validate critical components
            if (drawerLayout == null) {
                throw new IllegalStateException("DrawerLayout not found - check layout file structure");
            }

            // Set initial visibility states with advanced logic
            setAdvancedInitialVisibilityStates();

            Log.d(TAG, "Advanced view components initialized successfully");
        } catch (Exception e) {
            Log.e(TAG, "Error initializing view components", e);
            throw new RuntimeException("Failed to initialize view components", e);
        }
    }

    /**
     * Initialize data layer with comprehensive setup
     */
    private void initializeAdvancedDataLayer() {
        try {
            if (progressBar != null) {
                progressBar.setVisibility(View.VISIBLE);
            }

            // Initialize preferences for advanced state management
            preferences = getSharedPreferences(PREFS_NAME, MODE_PRIVATE);

            // Initialize executor service for background operations
            executor = Executors.newFixedThreadPool(2);

            // Initialize UI handler for thread communication
            uiHandler = new Handler(Looper.getMainLooper());

            // Initialize date formatter for display
            dateFormatter = new SimpleDateFormat("dd/MM/yyyy", Locale.getDefault());

            Log.d(TAG, "Advanced data layer initialized successfully");
        } catch (Exception e) {
            Log.e(TAG, "Error initializing data layer", e);
            handleAdvancedDataLayerError(e);
        }
    }

    /**
     * Setup advanced toolbar and navigation with comprehensive functionality
     */
    private void setupAdvancedToolbarAndNavigation() {
        try {
            // Setup toolbar with advanced configuration
            if (toolbar != null) {
                setSupportActionBar(toolbar);
                if (getSupportActionBar() != null) {
                    getSupportActionBar().setTitle("Thông tin văn bản");
                    getSupportActionBar().setDisplayHomeAsUpEnabled(true);
                }
            }

            // Setup advanced navigation drawer
            if (drawerLayout != null && toolbar != null) {
                drawerToggle = new ActionBarDrawerToggle(
                    this, drawerLayout, toolbar,
                    R.string.navigation_drawer_open,
                    R.string.navigation_drawer_close
                );
                drawerLayout.addDrawerListener(drawerToggle);
                drawerToggle.syncState();
            }

            // Setup navigation view
            if (navigationView != null) {
                navigationView.setNavigationItemSelectedListener(this);
            }

            Log.d(TAG, "Advanced toolbar and navigation setup completed");
        } catch (Exception e) {
            Log.e(TAG, "Error setting up toolbar and navigation", e);
        }
    }

    /**
     * Setup advanced document info layout with comprehensive UI components
     */
    private void setupAdvancedDocumentInfoLayout() {
        try {
            if (contentContainer != null) {
                // Create document information card
                createAdvancedDocumentCard();

                // Create statistics card
                createAdvancedStatisticsCard();

                // Create metadata card
                createAdvancedMetadataCard();
            }

            Log.d(TAG, "Advanced document info layout setup completed");
        } catch (Exception e) {
            Log.e(TAG, "Error setting up document info layout", e);
        }
    }

    /**
     * Create advanced document information card
     */
    private void createAdvancedDocumentCard() {
        try {
            documentCard = new MaterialCardView(this);
            documentCard.setCardElevation(8f);
            documentCard.setRadius(16f);
            documentCard.setUseCompatPadding(true);

            LinearLayout cardContent = new LinearLayout(this);
            cardContent.setOrientation(LinearLayout.VERTICAL);
            cardContent.setPadding(24, 24, 24, 24);

            // Document title
            TextView titleText = createAdvancedStyledTextView("THÔNG TIN VĂN BẢN", 20, true, Color.parseColor("#1976D2"));
            cardContent.addView(titleText);

            // Document name
            documentNameText = createAdvancedStyledTextView("Đang tải...", 18, true, Color.BLACK);
            cardContent.addView(documentNameText);

            // Document summary
            documentSummaryText = createAdvancedStyledTextView("Đang tải...", 14, false, Color.GRAY);
            cardContent.addView(documentSummaryText);

            documentCard.addView(cardContent);
            contentContainer.addView(documentCard);

        } catch (Exception e) {
            Log.e(TAG, "Error creating document card", e);
        }
    }

    /**
     * Create advanced statistics card
     */
    private void createAdvancedStatisticsCard() {
        try {
            statisticsCard = new MaterialCardView(this);
            statisticsCard.setCardElevation(8f);
            statisticsCard.setRadius(16f);
            statisticsCard.setUseCompatPadding(true);

            LinearLayout cardContent = new LinearLayout(this);
            cardContent.setOrientation(LinearLayout.VERTICAL);
            cardContent.setPadding(24, 24, 24, 24);

            // Statistics title
            TextView titleText = createAdvancedStyledTextView("THỐNG KÊ NỘI DUNG", 18, true, Color.parseColor("#388E3C"));
            cardContent.addView(titleText);

            // Chapter count
            chapterCountText = createAdvancedStyledTextView("Số chương: Đang tải...", 16, false, Color.BLACK);
            cardContent.addView(chapterCountText);

            // Article count
            articleCountText = createAdvancedStyledTextView("Số điều: Đang tải...", 16, false, Color.BLACK);
            cardContent.addView(articleCountText);

            // Document size
            documentSizeText = createAdvancedStyledTextView("Kích thước: Đang tải...", 16, false, Color.BLACK);
            cardContent.addView(documentSizeText);

            statisticsCard.addView(cardContent);
            contentContainer.addView(statisticsCard);

        } catch (Exception e) {
            Log.e(TAG, "Error creating statistics card", e);
        }
    }

    /**
     * Create advanced metadata card
     */
    private void createAdvancedMetadataCard() {
        try {
            metadataCard = new MaterialCardView(this);
            metadataCard.setCardElevation(8f);
            metadataCard.setRadius(16f);
            metadataCard.setUseCompatPadding(true);

            LinearLayout cardContent = new LinearLayout(this);
            cardContent.setOrientation(LinearLayout.VERTICAL);
            cardContent.setPadding(24, 24, 24, 24);

            // Metadata title
            TextView titleText = createAdvancedStyledTextView("THÔNG TIN PHÁT HÀNH", 18, true, Color.parseColor("#F57C00"));
            cardContent.addView(titleText);

            // Issuing authority
            issuingAuthorityText = createAdvancedStyledTextView("Cơ quan ban hành: Đang tải...", 16, false, Color.BLACK);
            cardContent.addView(issuingAuthorityText);

            // Issue date
            issueDateText = createAdvancedStyledTextView("Ngày ban hành: Đang tải...", 16, false, Color.BLACK);
            cardContent.addView(issueDateText);

            // Effective date
            effectiveDateText = createAdvancedStyledTextView("Ngày hiệu lực: Đang tải...", 16, false, Color.BLACK);
            cardContent.addView(effectiveDateText);

            // Last updated
            lastUpdatedText = createAdvancedStyledTextView("Cập nhật lần cuối: " + dateFormatter.format(new Date()), 14, false, Color.GRAY);
            cardContent.addView(lastUpdatedText);

            metadataCard.addView(cardContent);
            contentContainer.addView(metadataCard);

        } catch (Exception e) {
            Log.e(TAG, "Error creating metadata card", e);
        }
    }

    /**
     * Create advanced styled TextView with comprehensive formatting
     */
    private TextView createAdvancedStyledTextView(String text, int textSize, boolean isBold, int textColor) {
        TextView textView = new TextView(this);
        textView.setText(text);
        textView.setTextSize(textSize);
        textView.setTextColor(textColor);

        if (isBold) {
            textView.setTypeface(textView.getTypeface(), Typeface.BOLD);
        }

        // Add margins for better spacing
        LinearLayout.LayoutParams params = new LinearLayout.LayoutParams(
            LinearLayout.LayoutParams.MATCH_PARENT,
            LinearLayout.LayoutParams.WRAP_CONTENT
        );
        params.setMargins(0, 8, 0, 8);
        textView.setLayoutParams(params);

        return textView;
    }

    /**
     * Load advanced document information with comprehensive processing
     */
    private void loadAdvancedDocumentInformation() {
        try {
            if (executor != null) {
                executor.execute(() -> {
                    try {
                        // Load law data from JSON
                        JsonDataLoader loader = new JsonDataLoader(this);
                        lawEntities = loader.loadLawDataFromAssets();

                        if (lawEntities != null && !lawEntities.isEmpty()) {
                            // Process document information
                            processAdvancedDocumentData();

                            // Update UI on main thread
                            uiHandler.post(() -> updateAdvancedDocumentInfoUI());
                        } else {
                            uiHandler.post(() -> handleAdvancedDataLoadError(new Exception("No data found")));
                        }

                    } catch (Exception e) {
                        Log.e(TAG, "Error loading document information", e);
                        uiHandler.post(() -> handleAdvancedDataLoadError(e));
                    }
                });
            }
        } catch (Exception e) {
            Log.e(TAG, "Critical error starting document info load", e);
            handleAdvancedDataLoadError(e);
        }
    }

    /**
     * Process advanced document data with comprehensive analysis
     */
    private void processAdvancedDocumentData() {
        try {
            if (lawEntities != null) {
                documentInfo = new DocumentInfo();

                // Set document basic information
                documentInfo.setDocumentName("Bộ Luật Dân sự Việt Nam");
                documentInfo.setContentSummary("Bộ luật quy định về các quan hệ dân sự, bao gồm quyền và nghĩa vụ của các chủ thể trong các lĩnh vực dân sự");
                documentInfo.setIssuingAuthority("Quốc hội nước Cộng hòa xã hội chủ nghĩa Việt Nam");
                documentInfo.setIssueDate("24/11/2015");
                documentInfo.setEffectiveDate("01/01/2017");

                // Calculate statistics
                int chapterCount = 0;
                int articleCount = 0;
                int totalSize = 0;

                for (LawEntity entity : lawEntities) {
                    if (entity.isChapter()) {
                        chapterCount++;
                    } else if (entity.isArticle()) {
                        articleCount++;
                    }

                    if (entity.getContent() != null) {
                        totalSize += entity.getContent().length();
                    }
                    if (entity.getTitle() != null) {
                        totalSize += entity.getTitle().length();
                    }
                }

                documentInfo.setChapterCount(chapterCount);
                documentInfo.setArticleCount(articleCount);
                documentInfo.setDocumentSize(totalSize);

                Log.d(TAG, "Document data processed successfully");
            }
        } catch (Exception e) {
            Log.e(TAG, "Error processing document data", e);
        }
    }

    /**
     * Update advanced document info UI with comprehensive formatting
     */
    private void updateAdvancedDocumentInfoUI() {
        try {
            if (documentInfo != null) {
                // Update document information
                if (documentNameText != null) {
                    documentNameText.setText(documentInfo.getDocumentName());
                }

                if (documentSummaryText != null) {
                    documentSummaryText.setText(documentInfo.getContentSummary());
                }

                // Update statistics
                if (chapterCountText != null) {
                    chapterCountText.setText("Số chương: " + documentInfo.getChapterCount());
                }

                if (articleCountText != null) {
                    articleCountText.setText("Số điều: " + documentInfo.getArticleCount());
                }

                if (documentSizeText != null) {
                    String sizeText = formatAdvancedFileSize(documentInfo.getDocumentSize());
                    documentSizeText.setText("Kích thước: " + sizeText);
                }

                // Update metadata
                if (issuingAuthorityText != null) {
                    issuingAuthorityText.setText("Cơ quan ban hành: " + documentInfo.getIssuingAuthority());
                }

                if (issueDateText != null) {
                    issueDateText.setText("Ngày ban hành: " + documentInfo.getIssueDate());
                }

                if (effectiveDateText != null) {
                    effectiveDateText.setText("Ngày hiệu lực: " + documentInfo.getEffectiveDate());
                }
            }

            // Hide progress bar
            if (progressBar != null) {
                progressBar.setVisibility(View.GONE);
            }

            Log.d(TAG, "Document info UI updated successfully");
        } catch (Exception e) {
            Log.e(TAG, "Error updating document info UI", e);
        }
    }

    /**
     * Format advanced file size with appropriate units
     */
    private String formatAdvancedFileSize(long sizeInBytes) {
        if (sizeInBytes < 1024) {
            return sizeInBytes + " bytes";
        } else if (sizeInBytes < 1024 * 1024) {
            return String.format(Locale.getDefault(), "%.1f KB", sizeInBytes / 1024.0);
        } else {
            return String.format(Locale.getDefault(), "%.1f MB", sizeInBytes / (1024.0 * 1024.0));
        }
    }

    /**
     * Track advanced user interaction for analytics
     */
    private void trackAdvancedUserInteraction() {
        try {
            if (preferences != null) {
                // Increment view count
                int viewCount = preferences.getInt(KEY_VIEW_COUNT, 0) + 1;
                preferences.edit()
                    .putInt(KEY_VIEW_COUNT, viewCount)
                    .putLong(KEY_LAST_VIEW_TIME, System.currentTimeMillis())
                    .apply();

                Log.d(TAG, "User interaction tracked: view count = " + viewCount);
            }
        } catch (Exception e) {
            Log.e(TAG, "Error tracking user interaction", e);
        }
    }

    /**
     * Set advanced initial visibility states
     */
    private void setAdvancedInitialVisibilityStates() {
        if (progressBar != null) {
            progressBar.setVisibility(View.VISIBLE);
        }
    }

    /**
     * Handle advanced initialization error
     */
    private void handleAdvancedInitializationError(Exception e) {
        Log.e(TAG, "Initialization error", e);
        Toast.makeText(this, "Lỗi khởi tạo: " + e.getMessage(), Toast.LENGTH_LONG).show();
        finish();
    }

    /**
     * Handle advanced data layer error
     */
    private void handleAdvancedDataLayerError(Exception e) {
        Log.e(TAG, "Data layer error", e);
        Toast.makeText(this, "Lỗi dữ liệu: " + e.getMessage(), Toast.LENGTH_LONG).show();
    }

    /**
     * Handle advanced data load error
     */
    private void handleAdvancedDataLoadError(Exception e) {
        Log.e(TAG, "Data load error", e);
        Toast.makeText(this, "Lỗi tải thông tin văn bản: " + e.getMessage(), Toast.LENGTH_LONG).show();

        if (progressBar != null) {
            progressBar.setVisibility(View.GONE);
        }

        // Show default information
        showAdvancedDefaultInformation();
    }

    /**
     * Show advanced default information when data loading fails
     */
    private void showAdvancedDefaultInformation() {
        try {
            if (documentNameText != null) {
                documentNameText.setText("Bộ Luật Dân sự Việt Nam");
            }

            if (documentSummaryText != null) {
                documentSummaryText.setText("Không thể tải thông tin chi tiết");
            }

            if (chapterCountText != null) {
                chapterCountText.setText("Số chương: Không xác định");
            }

            if (articleCountText != null) {
                articleCountText.setText("Số điều: Không xác định");
            }

            if (issuingAuthorityText != null) {
                issuingAuthorityText.setText("Cơ quan ban hành: Quốc hội Việt Nam");
            }

            if (issueDateText != null) {
                issueDateText.setText("Ngày ban hành: 24/11/2015");
            }

            if (effectiveDateText != null) {
                effectiveDateText.setText("Ngày hiệu lực: 01/01/2017");
            }
        } catch (Exception e) {
            Log.e(TAG, "Error showing default information", e);
        }
    }

    @Override
    public boolean onNavigationItemSelected(@NonNull MenuItem item) {
        int id = item.getItemId();

        if (id == R.id.nav_home) {
            Intent intent = new Intent(this, MainActivity.class);
            intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP);
            startActivity(intent);
        } else if (id == R.id.nav_chapters) {
            Intent intent = new Intent(this, ChaptersActivity.class);
            startActivity(intent);
        } else if (id == R.id.nav_bookmarks) {
            Intent intent = new Intent(this, BookmarkActivity.class);
            startActivity(intent);
        } else if (id == R.id.nav_document_info) {
            // Already in this activity
            Toast.makeText(this, "Bạn đang ở trang Thông tin văn bản", Toast.LENGTH_SHORT).show();
        } else if (id == R.id.nav_share) {
            handleShareAction();
        } else if (id == R.id.nav_privacy) {
            handlePrivacyPolicyAction();
        }

        if (drawerLayout != null) {
            drawerLayout.closeDrawer(GravityCompat.START);
        }
        return true;
    }

    private void handleShareAction() {
        try {
            Intent shareIntent = new Intent(Intent.ACTION_SEND);
            shareIntent.setType("text/plain");
            shareIntent.putExtra(Intent.EXTRA_SUBJECT, "Luật Dân sự Việt Nam");
            shareIntent.putExtra(Intent.EXTRA_TEXT, 
                "Tải ứng dụng Luật Dân sự Việt Nam để tra cứu pháp luật dễ dàng!\n" +
                "https://play.google.com/store/apps/details?id=com.oriondev.luatdansu");
            
            Intent chooser = Intent.createChooser(shareIntent, "Chia sẻ ứng dụng qua");
            startActivity(chooser);
        } catch (Exception e) {
            Toast.makeText(this, "Không thể chia sẻ ứng dụng", Toast.LENGTH_SHORT).show();
        }
    }

    private void handlePrivacyPolicyAction() {
        try {
            Intent intent = new Intent(Intent.ACTION_VIEW);
            intent.setData(Uri.parse("https://sites.google.com/view/csbmi/home"));
            startActivity(intent);
        } catch (Exception e) {
            Toast.makeText(this, "Không thể mở chính sách bảo mật", Toast.LENGTH_SHORT).show();
        }
    }

    @Override
    public void onBackPressed() {
        if (drawerLayout != null && drawerLayout.isDrawerOpen(GravityCompat.START)) {
            drawerLayout.closeDrawer(GravityCompat.START);
        } else {
            super.onBackPressed();
        }
    }
}

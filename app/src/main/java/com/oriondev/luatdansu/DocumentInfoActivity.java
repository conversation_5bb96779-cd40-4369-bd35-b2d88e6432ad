package com.oriondev.luatdansu;

import android.content.Context;
import android.content.Intent;
import android.content.SharedPreferences;
import android.net.ConnectivityManager;
import android.net.NetworkInfo;
import android.os.Bundle;
import android.view.MenuItem;
import android.view.View;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.widget.Toolbar;
import androidx.core.view.GravityCompat;
import androidx.drawerlayout.widget.DrawerLayout;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.google.android.gms.ads.AdRequest;
import com.google.android.gms.ads.AdView;
import com.google.android.gms.ads.MobileAds;
import com.google.android.material.card.MaterialCardView;
import com.google.android.material.navigation.NavigationView;
import com.google.android.material.progressindicator.LinearProgressIndicator;
import com.google.android.material.snackbar.Snackbar;

import com.oriondev.luatdansu.adapter.DocumentInfoAdapter;
import com.oriondev.luatdansu.repository.LawRepository;
import com.oriondev.luatdansu.database.entity.LawEntity;
import com.oriondev.luatdansu.model.DocumentInfo;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

public class DocumentInfoActivity extends AppCompatActivity implements NavigationView.OnNavigationItemSelectedListener {

    // UI Components
    private RecyclerView recyclerView;
    private DocumentInfoAdapter adapter;
    private LinearProgressIndicator progressBar;
    private Toolbar toolbar;
    private DrawerLayout drawerLayout;
    private NavigationView navigationView;
    private AdView adView;
    private MaterialCardView adContainer;
    private MaterialCardView disclaimerBanner;

    // Data
    private ExecutorService executor;
    private SharedPreferences preferences;
    private DocumentInfo documentInfo;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_document_info);

        // Initialize components
        initializeViews();
        setupToolbar();
        setupRecyclerView();
        setupAds();

        // Initialize executor and preferences
        executor = Executors.newSingleThreadExecutor();
        preferences = getSharedPreferences("app_settings", MODE_PRIVATE);

        // Load document information
        loadDocumentInfo();
    }

    private void initializeViews() {
        recyclerView = findViewById(R.id.recyclerView);
        progressBar = findViewById(R.id.progressBar);
        toolbar = findViewById(R.id.toolbar);
        drawerLayout = findViewById(R.id.drawer_layout);
        navigationView = findViewById(R.id.navigation_view);
        adView = findViewById(R.id.adView);
        adContainer = findViewById(R.id.adContainer);
        disclaimerBanner = findViewById(R.id.disclaimerBanner);

        // Set initial visibility
        if (adContainer != null) {
            adContainer.setVisibility(View.GONE);
        }
        if (disclaimerBanner != null) {
            disclaimerBanner.setVisibility(View.VISIBLE);
        }
    }

    private void setupToolbar() {
        setSupportActionBar(toolbar);
        if (getSupportActionBar() != null) {
            getSupportActionBar().setDisplayHomeAsUpEnabled(true);
            getSupportActionBar().setTitle("Thông tin văn bản");
        }
    }

    private void setupRecyclerView() {
        adapter = new DocumentInfoAdapter(this);
        recyclerView.setLayoutManager(new LinearLayoutManager(this));
        recyclerView.setAdapter(adapter);
    }

    private void setupAds() {
        // Initialize AdMob
        MobileAds.initialize(this, initializationStatus -> {});

        if (isNetworkAvailable()) {
            loadAd();
        }
    }

    private void loadAd() {
        if (isNetworkAvailable() && adView != null) {
            // Build ad request with proper configuration
            AdRequest adRequest = new AdRequest.Builder()
                .build();

            adView.loadAd(adRequest);

            adView.setAdListener(new com.google.android.gms.ads.AdListener() {
                @Override
                public void onAdFailedToLoad(com.google.android.gms.ads.LoadAdError adError) {
                    super.onAdFailedToLoad(adError);
                    // Hide ad container when ad fails to load
                    if (adContainer != null) {
                        adContainer.setVisibility(View.GONE);
                    }
                }

                @Override
                public void onAdLoaded() {
                    super.onAdLoaded();
                    // Show ad container when ad loads successfully
                    if (adContainer != null) {
                        adContainer.setVisibility(View.VISIBLE);
                    }
                }

                @Override
                public void onAdOpened() {
                    super.onAdOpened();
                }

                @Override
                public void onAdClosed() {
                    super.onAdClosed();
                }

                @Override
                public void onAdClicked() {
                    super.onAdClicked();
                }
            });
        } else {
            // Hide ad container when network is not available
            if (adContainer != null) {
                adContainer.setVisibility(View.GONE);
            }
        }
    }

    private void loadDocumentInfo() {
        // Create sample document info for now
        documentInfo = createSampleDocumentInfo();

        if (documentInfo != null) {
            adapter.setDocumentInfo(documentInfo);
        } else {
            showError("Không thể tải thông tin văn bản");
        }
    }

    private DocumentInfo createSampleDocumentInfo() {
        DocumentInfo info = new DocumentInfo();

        // Set basic document information
        info.setDocumentTitle("Bộ luật Dân sự năm 2015");
        info.setIssuingAuthority("Quốc hội");
        info.setIssueDate("24/11/2015");
        info.setDocumentNumber("Luật số 91/2015/QH13");

        // Set structure counts (sample data)
        info.setChapterIndexCount(24);
        info.setArticleCount(689);
        info.setClauseCount(2156);
        info.setTotalItems(2869);

        // Set summary
        String summary = String.format(
            "Bộ luật Dân sự năm 2015 là văn bản pháp luật quan trọng, quy định về các quan hệ dân sự. " +
            "Văn bản gồm %d chương chỉ mục, %d điều khoản và %d khoản, tổng cộng %d mục nội dung.\n\n" +
            "%s\n\n%s",
            info.getChapterIndexCount(), info.getArticleCount(), info.getClauseCount(), info.getTotalItems(),
            getString(R.string.disclaimer),
            getString(R.string.source_info)
        );
        info.setSummary(summary);

        return info;
    }

    private DocumentInfo analyzeDocumentData(List<LawEntity> lawItems) {
        if (lawItems == null || lawItems.isEmpty()) {
            return null;
        }

        DocumentInfo info = new DocumentInfo();

        // Set basic document information
        info.setDocumentTitle("Bộ luật Dân sự năm 2015");
        info.setIssuingAuthority("Quốc hội");
        info.setIssueDate("24/11/2015");
        info.setDocumentNumber("Luật số 91/2015/QH13");

        // Analyze structure
        int chapterCount = 0;
        int articleCount = 0;
        int clauseCount = 0;
        int totalItems = lawItems.size();

        for (LawEntity item : lawItems) {
            String type = item.getType();
            if ("chapter".equals(type)) {
                chapterCount++;
            } else if ("article".equals(type)) {
                articleCount++;
            } else if ("clause".equals(type)) {
                clauseCount++;
            }
        }

        info.setChapterIndexCount(chapterCount);
        info.setArticleCount(articleCount);
        info.setClauseCount(clauseCount);
        info.setTotalItems(totalItems);

        // Set summary
        String summary = String.format(
            "Bộ luật Dân sự năm 2015 là văn bản pháp luật quan trọng, quy định về các quan hệ dân sự. " +
            "Văn bản gồm %d chương chỉ mục, %d điều khoản và %d khoản, tổng cộng %d mục nội dung.",
            chapterCount, articleCount, clauseCount, totalItems
        );
        info.setSummary(summary);

        return info;
    }

    private boolean isNetworkAvailable() {
        ConnectivityManager connectivityManager = (ConnectivityManager) getSystemService(Context.CONNECTIVITY_SERVICE);
        if (connectivityManager != null) {
            NetworkInfo activeNetworkInfo = connectivityManager.getActiveNetworkInfo();
            return activeNetworkInfo != null && activeNetworkInfo.isConnected();
        }
        return false;
    }

    private void showError(String message) {
        Toast.makeText(this, message, Toast.LENGTH_LONG).show();
    }

    private void showSnackbar(String message) {
        Snackbar.make(findViewById(android.R.id.content), message, Snackbar.LENGTH_SHORT).show();
    }

    @Override
    public boolean onOptionsItemSelected(@NonNull MenuItem item) {
        if (item.getItemId() == android.R.id.home) {
            finish();
            return true;
        }
        return super.onOptionsItemSelected(item);
    }

    @Override
    public boolean onNavigationItemSelected(@NonNull MenuItem item) {
        int id = item.getItemId();

        if (id == R.id.nav_home) {
            Intent intent = new Intent(this, MainActivity.class);
            intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP);
            startActivity(intent);
        } else if (id == R.id.nav_chapters) {
            Intent intent = new Intent(this, ChaptersActivity.class);
            startActivity(intent);
        } else if (id == R.id.nav_bookmarks) {
            Intent intent = new Intent(this, BookmarkActivity.class);
            startActivity(intent);
        } else if (id == R.id.nav_document_info) {
            // Already in this activity
            showSnackbar("Bạn đang ở trang Thông tin văn bản");
        }

        if (drawerLayout != null) {
            drawerLayout.closeDrawer(GravityCompat.START);
        }
        return true;
    }

    @Override
    protected void onDestroy() {
        if (executor != null && !executor.isShutdown()) {
            executor.shutdown();
        }
        super.onDestroy();
    }
}

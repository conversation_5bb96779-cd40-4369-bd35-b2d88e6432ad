package com.oriondev.luatdansu;

import android.content.Intent;
import android.content.SharedPreferences;
import android.graphics.Color;
import android.net.Uri;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.text.Editable;
import android.text.Spannable;
import android.text.SpannableString;
import android.text.TextWatcher;
import android.text.style.BackgroundColorSpan;
import android.text.style.ForegroundColorSpan;
import android.util.Log;
import android.view.MenuItem;
import android.view.View;
import android.widget.EditText;
import android.widget.ImageButton;
import android.widget.LinearLayout;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.appcompat.app.ActionBarDrawerToggle;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.view.GravityCompat;
import androidx.drawerlayout.widget.DrawerLayout;
import androidx.lifecycle.ViewModelProvider;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.google.android.material.appbar.MaterialToolbar;
import com.google.android.material.card.MaterialCardView;
import com.google.android.material.navigation.NavigationView;
import com.google.android.material.progressindicator.LinearProgressIndicator;
import com.oriondev.luatdansu.adapter.LawContentAdapter;
import com.oriondev.luatdansu.utils.JsonDataLoader;
import com.oriondev.luatdansu.database.entity.LawEntity;
import com.oriondev.luatdansu.repository.LawRepository;
import com.oriondev.luatdansu.viewmodel.MainViewModel;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

public class ChaptersActivity extends AppCompatActivity implements NavigationView.OnNavigationItemSelectedListener {

    private static final String TAG = "ChaptersActivity";
    private static final String PREFS_NAME = "ChaptersPrefs";
    private static final String KEY_LAST_SEARCH = "last_search_query";
    private static final String KEY_SCROLL_POSITION = "scroll_position";

    // Advanced UI Components with comprehensive functionality
    private RecyclerView recyclerView;
    private LinearLayout searchContainer;
    private EditText searchEditText;
    private ImageButton searchButton, searchClear;
    private LinearProgressIndicator progressBar;
    private MaterialCardView disclaimerBanner;
    private MaterialToolbar toolbar;
    private DrawerLayout drawerLayout;
    private NavigationView navigationView;
    private ActionBarDrawerToggle drawerToggle;
    private TextView chapterCountText, totalArticlesText;

    // Advanced Data Management and Business Logic
    private MainViewModel viewModel;
    private LawContentAdapter adapter;
    private LawRepository repository;
    private ExecutorService executor;
    private SharedPreferences preferences;
    private Handler searchHandler;
    private List<LawEntity> originalChapterEntities;
    private List<LawEntity> filteredChapterEntities;
    private String currentSearchQuery = "";
    private int lastScrollPosition = 0;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_main);

        try {
            Log.d(TAG, "Starting comprehensive ChaptersActivity initialization");

            // Initialize core components with advanced error handling
            initializeAdvancedViewComponents();
            initializeAdvancedDataLayer();
            setupAdvancedToolbarAndNavigation();
            setupAdvancedRecyclerView();
            setupAdvancedSearchSystem();
            setupAdvancedEventHandlers();
            setupAdvancedObservers();

            // Load and display chapters with comprehensive filtering
            loadAdvancedChapterData();

            // Restore previous state if available
            restoreAdvancedActivityState();

            Log.d(TAG, "ChaptersActivity initialization completed successfully");

        } catch (Exception e) {
            Log.e(TAG, "Critical error during ChaptersActivity initialization", e);
            handleAdvancedInitializationError(e);
        }
    }

    /**
     * Initialize view components with comprehensive null checking and validation
     */
    private void initializeAdvancedViewComponents() {
        try {
            // Core navigation components
            toolbar = findViewById(R.id.toolbar);
            drawerLayout = findViewById(R.id.drawer_layout);
            navigationView = findViewById(R.id.navigation_view);

            // Main content components
            recyclerView = findViewById(R.id.recyclerView);
            progressBar = findViewById(R.id.progressBar);

            // Search components with advanced functionality
            searchContainer = findViewById(R.id.search_container);
            searchEditText = findViewById(R.id.search_edit_text);
            searchButton = findViewById(R.id.search_button);
            searchClear = findViewById(R.id.search_clear);

            // Statistics and info components
            disclaimerBanner = findViewById(R.id.disclaimerBanner);

            // Validate critical components
            if (drawerLayout == null) {
                throw new IllegalStateException("DrawerLayout not found - check layout file structure");
            }
            if (recyclerView == null) {
                throw new IllegalStateException("RecyclerView not found - check layout file structure");
            }

            // Set initial visibility states with advanced logic
            setAdvancedInitialVisibilityStates();

            Log.d(TAG, "Advanced view components initialized successfully");
        } catch (Exception e) {
            Log.e(TAG, "Error initializing view components", e);
            throw new RuntimeException("Failed to initialize view components", e);
        }
    }

    /**
     * Initialize data layer with comprehensive repository and ViewModel setup
     */
    private void initializeAdvancedDataLayer() {
        try {
            if (progressBar != null) {
                progressBar.setVisibility(View.VISIBLE);
            }

            // Initialize preferences for advanced state management
            preferences = getSharedPreferences(PREFS_NAME, MODE_PRIVATE);

            // Initialize executor service for background operations
            executor = Executors.newFixedThreadPool(3);

            // Initialize search handler for debounced search
            searchHandler = new Handler(Looper.getMainLooper());

            // Initialize repository with advanced configuration
            repository = new LawRepository(this);

            // Initialize ViewModel with advanced lifecycle management
            viewModel = new ViewModelProvider(this).get(MainViewModel.class);

            // Initialize data collections
            originalChapterEntities = new ArrayList<>();
            filteredChapterEntities = new ArrayList<>();

            Log.d(TAG, "Advanced data layer initialized successfully");
        } catch (Exception e) {
            Log.e(TAG, "Error initializing data layer", e);
            handleAdvancedDataLayerError(e);
        }
    }

    /**
     * Setup advanced toolbar and navigation with comprehensive functionality
     */
    private void setupAdvancedToolbarAndNavigation() {
        try {
            // Setup toolbar with advanced configuration
            if (toolbar != null) {
                setSupportActionBar(toolbar);
                if (getSupportActionBar() != null) {
                    getSupportActionBar().setTitle("Danh sách chương");
                    getSupportActionBar().setDisplayHomeAsUpEnabled(true);
                }
            }

            // Setup advanced navigation drawer with sophisticated handling
            if (drawerLayout != null && toolbar != null) {
                drawerToggle = new ActionBarDrawerToggle(
                    this, drawerLayout, toolbar,
                    R.string.navigation_drawer_open,
                    R.string.navigation_drawer_close
                ) {
                    @Override
                    public void onDrawerClosed(View view) {
                        super.onDrawerClosed(view);
                        invalidateOptionsMenu();
                        Log.d(TAG, "Navigation drawer closed");
                    }

                    @Override
                    public void onDrawerOpened(View drawerView) {
                        super.onDrawerOpened(drawerView);
                        invalidateOptionsMenu();
                        Log.d(TAG, "Navigation drawer opened");
                    }
                };

                drawerLayout.addDrawerListener(drawerToggle);
                drawerToggle.syncState();
            }

            // Setup navigation view with comprehensive menu handling
            if (navigationView != null) {
                navigationView.setNavigationItemSelectedListener(this);
                setupAdvancedNavigationHeader();
            }

            Log.d(TAG, "Advanced toolbar and navigation setup completed");
        } catch (Exception e) {
            Log.e(TAG, "Error setting up toolbar and navigation", e);
            showAdvancedErrorDialog("Navigation Setup Error", e.getMessage());
        }
    }

    /**
     * Setup advanced RecyclerView with comprehensive functionality
     */
    private void setupAdvancedRecyclerView() {
        try {
            if (recyclerView != null) {
                // Advanced layout manager with optimization
                LinearLayoutManager layoutManager = new LinearLayoutManager(this);
                layoutManager.setOrientation(LinearLayoutManager.VERTICAL);
                recyclerView.setLayoutManager(layoutManager);

                // Advanced adapter with comprehensive functionality
                adapter = new LawContentAdapter(this, new LawContentAdapter.OnItemClickListener() {
                    @Override
                    public void onItemClick(LawEntity item) {
                        handleAdvancedChapterClick(item);
                    }

                    @Override
                    public void onItemLongClick(LawEntity item) {
                        handleAdvancedChapterLongClick(item);
                    }
                });

                recyclerView.setAdapter(adapter);

                // Advanced scroll listener for performance optimization
                recyclerView.addOnScrollListener(new RecyclerView.OnScrollListener() {
                    @Override
                    public void onScrolled(@NonNull RecyclerView recyclerView, int dx, int dy) {
                        super.onScrolled(recyclerView, dx, dy);
                        handleAdvancedScrolling(dx, dy);
                    }
                });

                Log.d(TAG, "Advanced RecyclerView setup completed");
            }
        } catch (Exception e) {
            Log.e(TAG, "Error setting up RecyclerView", e);
        }
    }

    /**
     * Setup intelligent search system with debouncing and advanced filtering
     */
    private void setupAdvancedSearchSystem() {
        try {
            if (searchEditText != null) {
                searchEditText.addTextChangedListener(new TextWatcher() {
                    @Override
                    public void beforeTextChanged(CharSequence s, int start, int count, int after) {
                        // Advanced pre-processing can be added here
                    }

                    @Override
                    public void onTextChanged(CharSequence s, int start, int before, int count) {
                        handleAdvancedSearchTextChange(s.toString());
                    }

                    @Override
                    public void afterTextChanged(Editable s) {
                        // Advanced post-processing can be added here
                    }
                });
            }

            Log.d(TAG, "Advanced search system setup completed");
        } catch (Exception e) {
            Log.e(TAG, "Error setting up search system", e);
        }
    }

    /**
     * Setup advanced event handlers with comprehensive functionality
     */
    private void setupAdvancedEventHandlers() {
        try {
            // Advanced search button handling
            if (searchButton != null) {
                searchButton.setOnClickListener(v -> toggleAdvancedSearchView());
            }

            // Advanced search clear handling
            if (searchClear != null) {
                searchClear.setOnClickListener(v -> clearAdvancedSearch());
            }

            Log.d(TAG, "Advanced event handlers setup completed");
        } catch (Exception e) {
            Log.e(TAG, "Error setting up event handlers", e);
        }
    }

    /**
     * Setup advanced observers with comprehensive data binding
     */
    private void setupAdvancedObservers() {
        try {
            if (viewModel != null) {
                // Observe chapters data with advanced processing
                viewModel.getAllChapters().observe(this, chapters -> {
                    if (chapters != null) {
                        handleAdvancedChaptersDataUpdate(chapters);
                    }
                });

                // Observe loading state with advanced UI updates
                viewModel.isLoading().observe(this, isLoading -> {
                    handleAdvancedLoadingStateChange(isLoading);
                });

                // Observe error state with advanced error handling
                viewModel.getErrorMessage().observe(this, errorMessage -> {
                    if (errorMessage != null && !errorMessage.isEmpty()) {
                        handleAdvancedErrorMessage(errorMessage);
                    }
                });
            } else {
                throw new IllegalStateException("ViewModel not initialized");
            }
        } catch (Exception e) {
            Log.e(TAG, "Critical error setting up observers", e);
            showAdvancedErrorDialog("Observer Setup Error", e.getMessage());
        }
    }

    /**
     * Load advanced chapter data with comprehensive processing
     */
    private void loadAdvancedChapterData() {
        try {
            if (executor != null && repository != null) {
                executor.execute(() -> {
                    try {
                        // Check if database has data
                        repository.checkAndInitializeDatabase(isEmpty -> {
                            if (isEmpty) {
                                Log.d(TAG, "Database is empty - loading chapters from JSON");
                                loadAdvancedChaptersFromJson();
                            } else {
                                Log.d(TAG, "Database has data - loading chapters from repository");
                                runOnUiThread(() -> {
                                    if (viewModel != null) {
                                        viewModel.loadAllChapters();
                                    }
                                });
                            }
                        });
                    } catch (Exception e) {
                        Log.e(TAG, "Error in chapter data loading", e);
                        runOnUiThread(() -> handleAdvancedDataLoadError(e));
                    }
                });
            } else {
                throw new IllegalStateException("Executor or Repository not initialized");
            }
        } catch (Exception e) {
            Log.e(TAG, "Critical error starting chapter data load", e);
            handleAdvancedDataLoadError(e);
        }
    }

    // Advanced helper methods with comprehensive functionality
    private void setAdvancedInitialVisibilityStates() {
        if (searchContainer != null) {
            searchContainer.setVisibility(View.GONE);
        }
        if (progressBar != null) {
            progressBar.setVisibility(View.VISIBLE);
        }
    }

    private void setupAdvancedNavigationHeader() {
        try {
            if (navigationView != null && navigationView.getHeaderCount() > 0) {
                View headerView = navigationView.getHeaderView(0);
                if (headerView != null) {
                    // Advanced header setup can be implemented here
                    Log.d(TAG, "Navigation header configured successfully");
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "Error setting up navigation header", e);
        }
    }

    private void handleAdvancedChapterClick(LawEntity chapter) {
        try {
            if (chapter != null) {
                // Navigate to chapter content with advanced parameters
                Intent intent = new Intent(this, MainActivity.class);
                intent.putExtra("chapter_number", chapter.getNumber());
                intent.putExtra("chapter_title", chapter.getTitle());
                intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP);
                startActivity(intent);

                // Save interaction for analytics
                saveAdvancedChapterInteraction(chapter);
            }
        } catch (Exception e) {
            Log.e(TAG, "Error handling chapter click", e);
            Toast.makeText(this, "Lỗi mở chương: " + e.getMessage(), Toast.LENGTH_SHORT).show();
        }
    }

    private void handleAdvancedChapterLongClick(LawEntity chapter) {
        try {
            if (chapter != null) {
                // Show advanced context menu or bookmark options
                showAdvancedChapterContextMenu(chapter);
            }
        } catch (Exception e) {
            Log.e(TAG, "Error handling chapter long click", e);
        }
    }

    private void handleAdvancedScrolling(int dx, int dy) {
        // Advanced scrolling behavior with performance optimization
        lastScrollPosition += dy;

        // Save scroll position for state restoration
        if (preferences != null) {
            preferences.edit().putInt(KEY_SCROLL_POSITION, lastScrollPosition).apply();
        }
    }

    private void handleAdvancedSearchTextChange(String query) {
        currentSearchQuery = query;

        // Debounced search with advanced filtering
        if (searchHandler != null) {
            searchHandler.removeCallbacksAndMessages(null);
            searchHandler.postDelayed(() -> performAdvancedChapterSearch(query), 300);
        }
    }

    private void toggleAdvancedSearchView() {
        try {
            if (searchContainer != null) {
                if (searchContainer.getVisibility() == View.VISIBLE) {
                    searchContainer.setVisibility(View.GONE);
                    clearAdvancedSearch();
                } else {
                    searchContainer.setVisibility(View.VISIBLE);
                    if (searchEditText != null) {
                        searchEditText.requestFocus();
                    }
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "Error toggling search view", e);
        }
    }

    private void clearAdvancedSearch() {
        try {
            if (searchEditText != null) {
                searchEditText.setText("");
            }
            currentSearchQuery = "";

            // Reset to show all chapters
            if (adapter != null && originalChapterEntities != null) {
                adapter.setLawItems(originalChapterEntities);
                filteredChapterEntities.clear();
                filteredChapterEntities.addAll(originalChapterEntities);
            }
        } catch (Exception e) {
            Log.e(TAG, "Error clearing search", e);
        }
    }

    private void performAdvancedChapterSearch(String query) {
        try {
            if (query == null || query.trim().isEmpty()) {
                clearAdvancedSearch();
                return;
            }

            if (originalChapterEntities != null && adapter != null) {
                List<LawEntity> searchResults = new ArrayList<>();
                String lowerQuery = query.toLowerCase().trim();

                for (LawEntity chapter : originalChapterEntities) {
                    if (matchesAdvancedSearchCriteria(chapter, lowerQuery)) {
                        searchResults.add(chapter);
                    }
                }

                filteredChapterEntities.clear();
                filteredChapterEntities.addAll(searchResults);
                adapter.setLawItems(searchResults);

                // Update search statistics
                updateAdvancedSearchStatistics(searchResults.size(), query);
            }
        } catch (Exception e) {
            Log.e(TAG, "Error performing chapter search", e);
        }
    }

    private boolean matchesAdvancedSearchCriteria(LawEntity chapter, String query) {
        if (chapter == null || query == null) return false;

        return (chapter.getTitle() != null && chapter.getTitle().toLowerCase().contains(query)) ||
               (chapter.getContent() != null && chapter.getContent().toLowerCase().contains(query)) ||
               (chapter.getNumber() != null && chapter.getNumber().toLowerCase().contains(query));
    }

    private void saveAdvancedChapterInteraction(LawEntity chapter) {
        // Save chapter interaction for analytics and recommendations
        if (preferences != null && chapter != null) {
            preferences.edit()
                .putString("last_accessed_chapter", chapter.getNumber())
                .putLong("last_access_time", System.currentTimeMillis())
                .apply();
        }
    }

    private void showAdvancedChapterContextMenu(LawEntity chapter) {
        // Implementation for advanced context menu
        Toast.makeText(this, "Context menu for: " + chapter.getTitle(), Toast.LENGTH_SHORT).show();
    }

    private void updateAdvancedSearchStatistics(int resultCount, String query) {
        // Update search statistics and save query
        if (preferences != null) {
            preferences.edit()
                .putString(KEY_LAST_SEARCH, query)
                .putInt("last_search_results", resultCount)
                .apply();
        }
    }

    private void restoreAdvancedActivityState() {
        try {
            if (preferences != null) {
                // Restore last search query
                String lastSearch = preferences.getString(KEY_LAST_SEARCH, "");
                if (!lastSearch.isEmpty() && searchEditText != null) {
                    searchEditText.setText(lastSearch);
                }

                // Restore scroll position
                lastScrollPosition = preferences.getInt(KEY_SCROLL_POSITION, 0);
            }
        } catch (Exception e) {
            Log.e(TAG, "Error restoring activity state", e);
        }
    }

    private void loadAdvancedChaptersFromJson() {
        try {
            JsonDataLoader loader = new JsonDataLoader(this);
            List<LawEntity> lawEntities = loader.loadLawDataFromAssets();

            if (lawEntities != null && !lawEntities.isEmpty()) {
                // Filter only chapters
                List<LawEntity> chapters = new ArrayList<>();
                for (LawEntity entity : lawEntities) {
                    if (entity.isChapter()) {
                        chapters.add(entity);
                    }
                }

                runOnUiThread(() -> {
                    originalChapterEntities.clear();
                    originalChapterEntities.addAll(chapters);
                    filteredChapterEntities.clear();
                    filteredChapterEntities.addAll(chapters);

                    if (adapter != null) {
                        adapter.setLawItems(chapters);
                    }

                    if (progressBar != null) {
                        progressBar.setVisibility(View.GONE);
                    }

                    Toast.makeText(this, "Đã tải " + chapters.size() + " chương", Toast.LENGTH_SHORT).show();
                });
            }
        } catch (Exception e) {
            Log.e(TAG, "Error loading chapters from JSON", e);
            runOnUiThread(() -> handleAdvancedDataLoadError(e));
        }
    }

    // Advanced data handling methods
    private void handleAdvancedChaptersDataUpdate(List<LawEntity> chapters) {
        try {
            if (chapters != null) {
                originalChapterEntities.clear();
                originalChapterEntities.addAll(chapters);
                filteredChapterEntities.clear();
                filteredChapterEntities.addAll(chapters);

                if (adapter != null) {
                    adapter.setLawItems(chapters);
                }

                updateAdvancedChapterStatistics(chapters);
            }
        } catch (Exception e) {
            Log.e(TAG, "Error handling chapters data update", e);
        }
    }

    private void handleAdvancedLoadingStateChange(Boolean isLoading) {
        try {
            if (progressBar != null) {
                progressBar.setVisibility(isLoading ? View.VISIBLE : View.GONE);
            }
        } catch (Exception e) {
            Log.e(TAG, "Error handling loading state change", e);
        }
    }

    private void handleAdvancedErrorMessage(String errorMessage) {
        try {
            Toast.makeText(this, "Lỗi: " + errorMessage, Toast.LENGTH_LONG).show();
            Log.e(TAG, "Error message received: " + errorMessage);
        } catch (Exception e) {
            Log.e(TAG, "Error handling error message", e);
        }
    }

    private void handleAdvancedInitializationError(Exception e) {
        Log.e(TAG, "Initialization error", e);
        Toast.makeText(this, "Lỗi khởi tạo: " + e.getMessage(), Toast.LENGTH_LONG).show();
        finish();
    }

    private void handleAdvancedDataLayerError(Exception e) {
        Log.e(TAG, "Data layer error", e);
        Toast.makeText(this, "Lỗi dữ liệu: " + e.getMessage(), Toast.LENGTH_LONG).show();
    }

    private void handleAdvancedDataLoadError(Exception e) {
        Log.e(TAG, "Data load error", e);
        Toast.makeText(this, "Lỗi tải dữ liệu: " + e.getMessage(), Toast.LENGTH_LONG).show();

        if (progressBar != null) {
            progressBar.setVisibility(View.GONE);
        }
    }

    private void showAdvancedErrorDialog(String title, String message) {
        try {
            Toast.makeText(this, title + ": " + message, Toast.LENGTH_LONG).show();
        } catch (Exception e) {
            Log.e(TAG, "Error showing error dialog", e);
        }
    }

    private void updateAdvancedChapterStatistics(List<LawEntity> chapters) {
        try {
            if (chapters != null && preferences != null) {
                preferences.edit()
                    .putInt("total_chapters", chapters.size())
                    .putLong("last_update_time", System.currentTimeMillis())
                    .apply();
            }
        } catch (Exception e) {
            Log.e(TAG, "Error updating chapter statistics", e);
        }
    }

    @Override
    public boolean onNavigationItemSelected(@NonNull MenuItem item) {
        int id = item.getItemId();

        if (id == R.id.nav_home) {
            Intent intent = new Intent(this, MainActivity.class);
            intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP);
            startActivity(intent);
        } else if (id == R.id.nav_chapters) {
            // Already in this activity
            Toast.makeText(this, "Bạn đang ở trang Danh sách chương", Toast.LENGTH_SHORT).show();
        } else if (id == R.id.nav_bookmarks) {
            Intent intent = new Intent(this, BookmarkActivity.class);
            startActivity(intent);
        } else if (id == R.id.nav_document_info) {
            Intent intent = new Intent(this, DocumentInfoActivity.class);
            startActivity(intent);
        } else if (id == R.id.nav_share) {
            handleShareAction();
        } else if (id == R.id.nav_privacy) {
            handlePrivacyPolicyAction();
        }

        if (drawerLayout != null) {
            drawerLayout.closeDrawer(GravityCompat.START);
        }
        return true;
    }

    private void handleShareAction() {
        try {
            Intent shareIntent = new Intent(Intent.ACTION_SEND);
            shareIntent.setType("text/plain");
            shareIntent.putExtra(Intent.EXTRA_SUBJECT, "Luật Dân sự Việt Nam");
            shareIntent.putExtra(Intent.EXTRA_TEXT,
                "Tải ứng dụng Luật Dân sự Việt Nam để tra cứu pháp luật dễ dàng!\n" +
                "https://play.google.com/store/apps/details?id=com.oriondev.luatdansu");

            Intent chooser = Intent.createChooser(shareIntent, "Chia sẻ ứng dụng qua");
            startActivity(chooser);
        } catch (Exception e) {
            Toast.makeText(this, "Không thể chia sẻ ứng dụng", Toast.LENGTH_SHORT).show();
        }
    }

    private void handlePrivacyPolicyAction() {
        try {
            Intent intent = new Intent(Intent.ACTION_VIEW);
            intent.setData(Uri.parse("https://sites.google.com/view/csbmi/home"));
            startActivity(intent);
        } catch (Exception e) {
            Toast.makeText(this, "Không thể mở chính sách bảo mật", Toast.LENGTH_SHORT).show();
        }
    }

    @Override
    public void onBackPressed() {
        if (drawerLayout != null && drawerLayout.isDrawerOpen(GravityCompat.START)) {
            drawerLayout.closeDrawer(GravityCompat.START);
        } else {
            super.onBackPressed();
        }
    }
}

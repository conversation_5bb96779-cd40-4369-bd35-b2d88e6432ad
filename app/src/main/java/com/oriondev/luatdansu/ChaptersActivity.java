package com.oriondev.luatdansu;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import android.util.Log;
import android.view.MenuItem;
import android.view.View;
import android.widget.ProgressBar;
import android.widget.TextView;

import androidx.appcompat.app.AppCompatActivity;
import androidx.lifecycle.ViewModelProvider;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.google.android.material.appbar.MaterialToolbar;
import com.oriondev.luatdansu.database.entity.LawEntity;
import com.oriondev.luatdansu.viewmodel.MainViewModel;

import java.util.ArrayList;
import java.util.List;

public class ChaptersActivity extends AppCompatActivity {

    private static final String TAG = "ChaptersActivity";

    private RecyclerView recyclerView;
    private ChapterAdapter adapter;
    private MaterialToolbar toolbar;
    private ProgressBar progressBar;
    private TextView emptyView;
    private MainViewModel viewModel;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_chapters);

        initializeViews();
        initializeViewModel();
        setupToolbar();
        setupRecyclerView();
        setupObservers();
        loadChapters();
    }

    private void initializeViews() {
        toolbar = findViewById(R.id.toolbar);
        recyclerView = findViewById(R.id.recycler_view);
        progressBar = findViewById(R.id.progress_bar);
        emptyView = findViewById(R.id.empty_view);
    }

    private void initializeViewModel() {
        viewModel = new ViewModelProvider(this).get(MainViewModel.class);
        viewModel.initializeDatabase();
    }

    private void setupToolbar() {
        setSupportActionBar(toolbar);
        if (getSupportActionBar() != null) {
            getSupportActionBar().setDisplayHomeAsUpEnabled(true);
            getSupportActionBar().setTitle("Danh sách chương");
        }
    }

    private void setupRecyclerView() {
        recyclerView.setLayoutManager(new LinearLayoutManager(this));
        adapter = new ChapterAdapter(this::onChapterClick);
        recyclerView.setAdapter(adapter);
    }

    private void setupObservers() {
        // Observe chapters from database
        viewModel.getAllChapters().observe(this, chapters -> {
            Log.d(TAG, "Received chapters from database: " + (chapters != null ? chapters.size() : 0));

            if (chapters != null && !chapters.isEmpty()) {
                // Log raw chapters for debugging
                for (LawEntity entity : chapters) {
                    Log.d(TAG, "Raw chapter: " + entity.getNumber() + " - " + entity.getTitle());
                }

                List<Chapter> chapterList = convertToChapterList(chapters);
                Log.d(TAG, "Filtered chapters: " + chapterList.size());

                if (!chapterList.isEmpty()) {
                    adapter.setChapters(chapterList);
                    showContent();
                } else {
                    Log.w(TAG, "All chapters filtered out - showing empty");
                    showEmpty();
                }
            } else {
                Log.w(TAG, "No chapters received from database");
                showEmpty();
            }
        });

        // Observe loading state
        viewModel.isLoading().observe(this, isLoading -> {
            Log.d(TAG, "Loading state: " + isLoading);
            if (isLoading != null) {
                if (isLoading) {
                    showLoading();
                } else {
                    hideLoading();
                }
            }
        });
    }

    private void loadChapters() {
        // Trigger loading from database
        viewModel.loadAllChapters();
    }

    private List<Chapter> convertToChapterList(List<LawEntity> lawEntities) {
        List<Chapter> chapters = new ArrayList<>();
        List<Chapter> allChapters = new ArrayList<>(); // Backup list

        for (LawEntity entity : lawEntities) {
            if (entity.isChapter()) {
                String title = formatChapterTitle(entity);
                String description = formatChapterDescription(entity);
                String anchor = "#chuong_" + entity.getNumber();

                Chapter chapter = new Chapter(title, description, anchor);
                allChapters.add(chapter); // Add to backup

                if (hasValidContent(entity)) {
                    chapters.add(chapter); // Add to filtered list
                }
            }
        }

        // Fallback: if filtered list is too small, use all chapters
        if (chapters.size() < 3 && allChapters.size() > chapters.size()) {
            Log.w(TAG, "Filtered list too small (" + chapters.size() + "), using all chapters (" + allChapters.size() + ")");
            return allChapters;
        }

        return chapters;
    }

    private boolean hasValidContent(LawEntity entity) {
        // Kiểm tra xem chương có nội dung hợp lệ không
        if (entity == null) {
            Log.d(TAG, "Entity is null");
            return false;
        }

        Log.d(TAG, "Checking entity: " + entity.getNumber() + " - Title: '" + entity.getTitle() + "' - Content: '" +
              (entity.getContent() != null ? entity.getContent().substring(0, Math.min(50, entity.getContent().length())) : "null") + "'");

        // Chỉ chấp nhận chương có số La Mã (I, II, III, IV, V, etc.)
        String number = entity.getNumber();
        if (number == null || !isRomanNumeral(number)) {
            Log.d(TAG, "Not a valid chapter number (not Roman): " + number);
            return false;
        }

        // Kiểm tra title có nội dung thực sự và viết hoa
        if (entity.getTitle() != null && !entity.getTitle().trim().isEmpty()) {
            String title = entity.getTitle().trim();

            // Loại bỏ những title không hợp lệ
            if (isValidChapterTitle(title)) {
                Log.d(TAG, "Valid chapter found: " + number + " - " + title);
                return true;
            }
        }

        // Kiểm tra content và extract title từ content
        if (entity.getContent() != null && !entity.getContent().trim().isEmpty()) {
            String content = entity.getContent().trim();

            // Extract title từ content nếu có format "Chương X TITLE"
            if (content.startsWith("Chương " + number)) {
                String extractedTitle = extractTitleFromContent(content, number);
                if (extractedTitle != null && isValidChapterTitle(extractedTitle)) {
                    Log.d(TAG, "Valid chapter content found: " + number + " - " + extractedTitle);
                    return true;
                }
            }
        }

        Log.d(TAG, "No valid content found for chapter: " + number);
        return false;
    }

    private boolean isValidChapterTitle(String title) {
        if (title == null || title.trim().isEmpty()) {
            return false;
        }

        title = title.trim();

        // Loại bỏ những title không hợp lệ
        if (title.length() < 10) { // Quá ngắn
            return false;
        }

        // Loại bỏ những title chỉ chứa "của Bộ luật này" hoặc tương tự
        if (title.toLowerCase().contains("của bộ luật này") ||
            title.toLowerCase().contains("hoặc chưa được thực hiện") ||
            title.toLowerCase().matches(".*của bộ luật.*")) {
            return false;
        }

        // Chỉ chấp nhận title có ít nhất 50% chữ viết hoa
        int uppercaseCount = 0;
        int letterCount = 0;

        for (char c : title.toCharArray()) {
            if (Character.isLetter(c)) {
                letterCount++;
                if (Character.isUpperCase(c)) {
                    uppercaseCount++;
                }
            }
        }

        // Cần ít nhất 50% chữ cái là viết hoa và có ít nhất 5 chữ cái
        return letterCount >= 5 && (uppercaseCount * 100.0 / letterCount) >= 50;
    }

    private String extractTitleFromContent(String content, String chapterNumber) {
        if (content == null || chapterNumber == null) {
            return null;
        }

        String prefix = "Chương " + chapterNumber;
        if (content.startsWith(prefix)) {
            String remaining = content.substring(prefix.length()).trim();

            // Tìm phần title (thường là phần đầu trước dấu xuống dòng hoặc dấu chấm)
            String[] parts = remaining.split("\\n|\\.|;");
            if (parts.length > 0) {
                String title = parts[0].trim();
                if (title.length() > 5) {
                    return title;
                }
            }
        }

        return null;
    }

    private boolean isRomanNumeral(String str) {
        if (str == null || str.trim().isEmpty()) {
            return false;
        }
        // Kiểm tra xem có phải số La Mã không (I, II, III, IV, V, VI, VII, VIII, IX, X, XI, XII, etc.)
        // Hỗ trợ đầy đủ các số La Mã từ I đến XXXIX (1-39)
        return str.trim().matches("^(XL|L?X{0,3})(IX|IV|V?I{0,3})$");
    }

    private String formatChapterTitle(LawEntity entity) {
        if (entity.getNumber() != null && !entity.getNumber().trim().isEmpty()) {
            return "Chương " + entity.getNumber().trim();
        }
        return "Chương";
    }

    private String formatChapterDescription(LawEntity entity) {
        // Ưu tiên title nếu hợp lệ (có chữ viết hoa)
        if (entity.getTitle() != null && !entity.getTitle().trim().isEmpty()) {
            String title = entity.getTitle().trim();
            if (isValidChapterTitle(title)) {
                return formatDescription(title);
            }
        }

        // Extract title từ content
        if (entity.getContent() != null && !entity.getContent().trim().isEmpty()) {
            String content = entity.getContent().trim();

            if (content.startsWith("Chương") && entity.getNumber() != null) {
                String extractedTitle = extractTitleFromContent(content, entity.getNumber());
                if (extractedTitle != null && isValidChapterTitle(extractedTitle)) {
                    return formatDescription(extractedTitle);
                }
            }
        }

        // Fallback: sử dụng title gốc (ngay cả khi không hợp lệ)
        if (entity.getTitle() != null && !entity.getTitle().trim().isEmpty()) {
            return formatDescription(entity.getTitle().trim());
        }

        return "Nội dung chương";
    }

    private String formatDescription(String text) {
        if (text == null || text.trim().isEmpty()) {
            return "Nội dung chương";
        }

        text = text.trim();

        // Loại bỏ "Chương X" ở đầu nếu có
        text = text.replaceFirst("^Chương\\s+[IVX0-9]+\\s*", "").trim();

        // Làm sạch text - loại bỏ các ký tự không cần thiết
        text = text.replaceAll("\\s+", " "); // Normalize whitespace

        // Truncate nếu quá dài
        if (text.length() > 100) {
            text = text.substring(0, 97) + "...";
        }

        return text.isEmpty() ? "Nội dung chương" : text;
    }

    private void showLoading() {
        if (progressBar != null) {
            progressBar.setVisibility(View.VISIBLE);
        }
        if (recyclerView != null) {
            recyclerView.setVisibility(View.GONE);
        }
        if (emptyView != null) {
            emptyView.setVisibility(View.GONE);
        }
    }

    private void hideLoading() {
        if (progressBar != null) {
            progressBar.setVisibility(View.GONE);
        }
    }

    private void showContent() {
        if (recyclerView != null) {
            recyclerView.setVisibility(View.VISIBLE);
        }
        if (emptyView != null) {
            emptyView.setVisibility(View.GONE);
        }
    }

    private void showEmpty() {
        if (recyclerView != null) {
            recyclerView.setVisibility(View.GONE);
        }
        if (emptyView != null) {
            emptyView.setVisibility(View.VISIBLE);
            emptyView.setText("Không tìm thấy chương nào");
        }
    }

    private void onChapterClick(Chapter chapter) {
        Intent intent = new Intent();
        intent.putExtra("chapter_anchor", chapter.getAnchor());
        setResult(RESULT_OK, intent);
        finish();
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        if (item.getItemId() == android.R.id.home) {
            onBackPressed();
            return true;
        }
        return super.onOptionsItemSelected(item);
    }
}

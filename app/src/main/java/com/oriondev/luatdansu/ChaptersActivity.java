package com.oriondev.luatdansu;

import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import android.view.MenuItem;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.view.GravityCompat;
import androidx.drawerlayout.widget.DrawerLayout;

import com.google.android.material.navigation.NavigationView;

public class ChaptersActivity extends AppCompatActivity implements NavigationView.OnNavigationItemSelectedListener {

    private DrawerLayout drawerLayout;
    private NavigationView navigationView;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_main);

        initializeComponents();
    }

    private void initializeComponents() {
        drawerLayout = findViewById(R.id.drawer_layout);
        navigationView = findViewById(R.id.navigation_view);

        if (navigationView != null) {
            navigationView.setNavigationItemSelectedListener(this);
        }

        // Show message that this is chapters page
        Toast.makeText(this, "Trang Danh sách chương", Toast.LENGTH_SHORT).show();
    }

    @Override
    public boolean onNavigationItemSelected(@NonNull MenuItem item) {
        int id = item.getItemId();

        if (id == R.id.nav_home) {
            Intent intent = new Intent(this, MainActivity.class);
            intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP);
            startActivity(intent);
        } else if (id == R.id.nav_chapters) {
            // Already in this activity
            Toast.makeText(this, "Bạn đang ở trang Danh sách chương", Toast.LENGTH_SHORT).show();
        } else if (id == R.id.nav_bookmarks) {
            Intent intent = new Intent(this, BookmarkActivity.class);
            startActivity(intent);
        } else if (id == R.id.nav_document_info) {
            Intent intent = new Intent(this, DocumentInfoActivity.class);
            startActivity(intent);
        } else if (id == R.id.nav_share) {
            handleShareAction();
        } else if (id == R.id.nav_privacy) {
            handlePrivacyPolicyAction();
        }

        if (drawerLayout != null) {
            drawerLayout.closeDrawer(GravityCompat.START);
        }
        return true;
    }

    private void handleShareAction() {
        try {
            Intent shareIntent = new Intent(Intent.ACTION_SEND);
            shareIntent.setType("text/plain");
            shareIntent.putExtra(Intent.EXTRA_SUBJECT, "Luật Dân sự Việt Nam");
            shareIntent.putExtra(Intent.EXTRA_TEXT,
                "Tải ứng dụng Luật Dân sự Việt Nam để tra cứu pháp luật dễ dàng!\n" +
                "https://play.google.com/store/apps/details?id=com.oriondev.luatdansu");

            Intent chooser = Intent.createChooser(shareIntent, "Chia sẻ ứng dụng qua");
            startActivity(chooser);
        } catch (Exception e) {
            Toast.makeText(this, "Không thể chia sẻ ứng dụng", Toast.LENGTH_SHORT).show();
        }
    }

    private void handlePrivacyPolicyAction() {
        try {
            Intent intent = new Intent(Intent.ACTION_VIEW);
            intent.setData(Uri.parse("https://sites.google.com/view/csbmi/home"));
            startActivity(intent);
        } catch (Exception e) {
            Toast.makeText(this, "Không thể mở chính sách bảo mật", Toast.LENGTH_SHORT).show();
        }
    }

    @Override
    public void onBackPressed() {
        if (drawerLayout != null && drawerLayout.isDrawerOpen(GravityCompat.START)) {
            drawerLayout.closeDrawer(GravityCompat.START);
        } else {
            super.onBackPressed();
        }
    }
}

package com.oriondev.luatdansu;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.net.ConnectivityManager;
import android.net.NetworkInfo;
import android.os.Bundle;
import android.view.View;
import android.view.Window;
import android.view.WindowInsetsController;
import android.webkit.WebChromeClient;
import android.webkit.WebResourceError;
import android.webkit.WebResourceRequest;
import android.webkit.WebSettings;
import android.webkit.WebView;
import android.webkit.WebViewClient;
import android.widget.EditText;
import android.widget.ImageButton;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.TextView;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;
import androidx.core.view.WindowCompat;
import androidx.core.view.WindowInsetsCompat;
import androidx.core.view.WindowInsetsControllerCompat;

import com.google.android.gms.ads.AdRequest;
import com.google.android.gms.ads.AdView;
import com.google.android.gms.ads.MobileAds;

public class MainActivity extends AppCompatActivity {

    private WebView webView;
    private EditText searchText;
    private ImageButton btnSearch, btnHome, btnPrivacyPolicy;
    private AdView adView;
    private ProgressBar progressBar;
    private TextView adLabel;
    private LinearLayout adContainer;
    private BroadcastReceiver connectivityReceiver;
    private WindowInsetsControllerCompat windowInsetsController;

    // URL mặc định (trang Home của ứng dụng)
    private static final String HOME_URL = "file:///android_asset/luat.html";

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        
        // Enable edge-to-edge display
        WindowCompat.setDecorFitsSystemWindows(getWindow(), false);
        
        setContentView(R.layout.activity_main);

        // Set up edge-to-edge display
        windowInsetsController = WindowCompat.getInsetsController(getWindow(), getWindow().getDecorView());
        windowInsetsController.setAppearanceLightNavigationBars(true);
        windowInsetsController.setAppearanceLightStatusBars(true);
        windowInsetsController.setSystemBarsBehavior(
            WindowInsetsControllerCompat.BEHAVIOR_SHOW_TRANSIENT_BARS_BY_SWIPE
        );

        // Khởi tạo AdMob SDK
        MobileAds.initialize(this, initializationStatus -> {});

        // Ánh xạ các thành phần UI
        initializeViews();

        // Thiết lập BroadcastReceiver để lắng nghe thay đổi kết nối mạng
        setupConnectivityReceiver();

        // Cấu hình WebView
        setupWebView();

        // Thiết lập các sự kiện
        setupEventListeners();

        // Cập nhật trạng thái quảng cáo ban đầu
        updateAdVisibility();

        // Đảm bảo thanh điều hướng luôn hiển thị
        getWindow().getDecorView().setSystemUiVisibility(
            View.SYSTEM_UI_FLAG_LAYOUT_STABLE |
            View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
        );
    }

    private void initializeViews() {
        webView = findViewById(R.id.webView);
        searchText = findViewById(R.id.searchText);
        btnSearch = findViewById(R.id.btnSearch);
        btnHome = findViewById(R.id.btnHome);
        btnPrivacyPolicy = findViewById(R.id.btnPrivacyPolicy);
        adView = findViewById(R.id.adView);
        progressBar = findViewById(R.id.progressBar);
        adLabel = findViewById(R.id.adLabel);
        adContainer = findViewById(R.id.adContainer);

        // Đặt visibility ban đầu cho container quảng cáo là GONE
        if (adContainer != null) {
            adContainer.setVisibility(View.GONE);
        }
    }

    private void setupConnectivityReceiver() {
        connectivityReceiver = new BroadcastReceiver() {
            @Override
            public void onReceive(Context context, Intent intent) {
                if (ConnectivityManager.CONNECTIVITY_ACTION.equals(intent.getAction())) {
                    updateAdVisibility();
                }
            }
        };
        IntentFilter filter = new IntentFilter(ConnectivityManager.CONNECTIVITY_ACTION);
        registerReceiver(connectivityReceiver, filter);
    }

    private void setupWebView() {
        WebSettings webSettings = webView.getSettings();
        
        // Cấu hình bảo mật
        webSettings.setJavaScriptEnabled(true);
        webSettings.setDomStorageEnabled(true);
        webSettings.setAllowFileAccess(true);
        webSettings.setAllowContentAccess(true);
        webSettings.setAllowFileAccessFromFileURLs(false);
        webSettings.setAllowUniversalAccessFromFileURLs(false);
        
        // Cấu hình hiệu suất
        webSettings.setCacheMode(WebSettings.LOAD_DEFAULT);
        webSettings.setMixedContentMode(WebSettings.MIXED_CONTENT_ALWAYS_ALLOW);
        
        // Thiết lập WebViewClient
        webView.setWebViewClient(new WebViewClient() {
            @Override
            public void onPageStarted(WebView view, String url, android.graphics.Bitmap favicon) {
                super.onPageStarted(view, url, favicon);
                progressBar.setVisibility(View.VISIBLE);
            }

            @Override
            public void onPageFinished(WebView view, String url) {
                super.onPageFinished(view, url);
                progressBar.setVisibility(View.GONE);
                updateAdVisibility();
            }

            @Override
            public void onReceivedError(WebView view, WebResourceRequest request, WebResourceError error) {
                super.onReceivedError(view, request, error);
                progressBar.setVisibility(View.GONE);
                showError("Không thể tải trang. Vui lòng kiểm tra kết nối internet.");
                updateAdVisibility();
            }
        });

        // Thiết lập WebChromeClient
        webView.setWebChromeClient(new WebChromeClient() {
            @Override
            public void onProgressChanged(WebView view, int newProgress) {
                progressBar.setProgress(newProgress);
            }
        });

        // Load trang Home mặc định
        if (isNetworkAvailable()) {
            webView.loadUrl(HOME_URL);
        } else {
            showError("Không có kết nối internet. Vui lòng kiểm tra lại.");
        }
    }

    private void updateAdVisibility() {
        runOnUiThread(() -> {
            boolean isConnected = isNetworkAvailable();
            if (!isConnected) {
                // Khi không có kết nối, ẩn toàn bộ container
                if (adContainer != null) {
                    adContainer.setVisibility(View.GONE);
                }
            } else {
                // Khi có kết nối, thử load quảng cáo
                loadAd();
            }
        });
    }

    private void loadAd() {
        if (isNetworkAvailable() && adView != null) {
            AdRequest adRequest = new AdRequest.Builder().build();
            adView.loadAd(adRequest);
            
            // Thêm listener để xử lý các sự kiện quảng cáo
            adView.setAdListener(new com.google.android.gms.ads.AdListener() {
                @Override
                public void onAdFailedToLoad(com.google.android.gms.ads.LoadAdError adError) {
                    super.onAdFailedToLoad(adError);
                    // Ẩn container khi không load được quảng cáo
                    if (adContainer != null) {
                        adContainer.setVisibility(View.GONE);
                    }
                }

                @Override
                public void onAdLoaded() {
                    super.onAdLoaded();
                    // Hiển thị container khi quảng cáo đã load thành công
                    if (adContainer != null) {
                        adContainer.setVisibility(View.VISIBLE);
                    }
                }
            });
        } else {
            if (adContainer != null) {
                adContainer.setVisibility(View.GONE);
            }
        }
    }

    private void setupEventListeners() {
        // Xử lý sự kiện tìm kiếm trong WebView
        btnSearch.setOnClickListener(v -> {
            String query = searchText.getText().toString();
            if (!query.isEmpty()) {
                webView.findAllAsync(query);
            }
        });

        // Xử lý khi nhấn nút Home
        btnHome.setOnClickListener(v -> {
            String currentUrl = webView.getUrl();
            if (HOME_URL.equals(currentUrl)) {
                webView.scrollTo(0, 0);
            } else {
                webView.loadUrl(HOME_URL);
            }
        });

        // Xử lý mở Privacy Policy
        btnPrivacyPolicy.setOnClickListener(v -> webView.loadUrl("file:///android_asset/privacy_policy.html"));
    }

    private boolean isNetworkAvailable() {
        ConnectivityManager connectivityManager = (ConnectivityManager) getSystemService(Context.CONNECTIVITY_SERVICE);
        if (connectivityManager != null) {
            NetworkInfo activeNetworkInfo = connectivityManager.getActiveNetworkInfo();
            return activeNetworkInfo != null && activeNetworkInfo.isConnected();
        }
        return false;
    }

    private void showError(String message) {
        Toast.makeText(this, message, Toast.LENGTH_LONG).show();
    }

    @Override
    public void onBackPressed() {
        if (webView.canGoBack()) {
            webView.goBack();
        } else {
            super.onBackPressed();
        }
    }

    @Override
    protected void onResume() {
        super.onResume();
        updateAdVisibility();
        
        // Đảm bảo thanh điều hướng hiển thị khi resume
        getWindow().getDecorView().setSystemUiVisibility(
            View.SYSTEM_UI_FLAG_LAYOUT_STABLE |
            View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
        );
    }

    @Override
    protected void onDestroy() {
        if (connectivityReceiver != null) {
            unregisterReceiver(connectivityReceiver);
        }
        super.onDestroy();
    }

    @Override
    public void onConfigurationChanged(android.content.res.Configuration newConfig) {
        super.onConfigurationChanged(newConfig);
        // Cập nhật lại kích thước quảng cáo khi xoay màn hình
        if (adView != null) {
            loadAd();
        }
    }
}

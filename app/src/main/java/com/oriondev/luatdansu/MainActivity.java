package com.oriondev.luatdansu;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.SharedPreferences;
import android.net.ConnectivityManager;
import android.net.NetworkInfo;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.text.Editable;
import android.text.TextWatcher;
import android.view.MenuItem;
import android.view.View;
import android.view.inputmethod.EditorInfo;
import android.widget.EditText;
import android.widget.ImageButton;
import android.widget.LinearLayout;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.appcompat.app.ActionBarDrawerToggle;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.splashscreen.SplashScreen;
import androidx.core.view.GravityCompat;
import androidx.core.view.WindowCompat;
import androidx.drawerlayout.widget.DrawerLayout;
import androidx.lifecycle.ViewModelProvider;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.google.android.gms.ads.AdRequest;
import com.google.android.gms.ads.AdView;
import com.google.android.gms.ads.MobileAds;
import com.google.android.material.appbar.MaterialToolbar;
import com.google.android.material.button.MaterialButton;
import com.google.android.material.card.MaterialCardView;
import com.google.android.material.navigation.NavigationView;
import com.google.android.material.progressindicator.LinearProgressIndicator;
import com.google.android.material.snackbar.Snackbar;

import com.oriondev.luatdansu.adapter.LawContentAdapter;
import com.oriondev.luatdansu.database.entity.LawEntity;
import com.oriondev.luatdansu.database.repository.LawRepository;
import com.oriondev.luatdansu.utils.JsonDataLoader;
import com.oriondev.luatdansu.viewmodel.MainViewModel;

import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

public class MainActivity extends AppCompatActivity implements NavigationView.OnNavigationItemSelectedListener {

    // UI Components - NO WEBVIEW ANYWHERE
    private RecyclerView recyclerView;
    private LinearLayout searchContainer;
    private EditText searchEditText;
    private ImageButton searchButton, searchClear;
    private MaterialButton btnSearch, btnHome, btnBookmark, btnShare, btnSettings;
    private AdView adView;
    private LinearProgressIndicator progressBar;
    private MaterialCardView adContainer;
    private MaterialCardView disclaimerBanner;
    private MaterialToolbar toolbar;
    private DrawerLayout drawerLayout;
    private NavigationView navigationView;
    private ActionBarDrawerToggle drawerToggle;

    // Data and Logic - NATIVE ONLY
    private MainViewModel viewModel;
    private LawContentAdapter adapter;
    private BroadcastReceiver connectivityReceiver;
    private SharedPreferences preferences;
    private ExecutorService executor;

    // Search optimization
    private Handler searchHandler;
    private Runnable searchRunnable;
    private static final int SEARCH_DELAY_MS = 300;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        // Handle the splash screen transition
        SplashScreen splashScreen = SplashScreen.installSplashScreen(this);

        super.onCreate(savedInstanceState);

        // Enable edge-to-edge display
        WindowCompat.setDecorFitsSystemWindows(getWindow(), false);

        setContentView(R.layout.activity_main);

        // Initialize AdMob SDK
        MobileAds.initialize(this, initializationStatus -> {
            // AdMob SDK initialized
        });

        // Initialize components - ALL NATIVE, NO WEBVIEW
        initializeViews();
        initializeViewModel();
        setupToolbarAndNavigation();
        setupRecyclerView();
        setupConnectivityReceiver();
        setupEventListeners();
        setupObservers();

        // Initialize SharedPreferences
        preferences = getSharedPreferences("app_settings", MODE_PRIVATE);
        executor = Executors.newFixedThreadPool(2);

        // Initialize search handler
        searchHandler = new Handler(Looper.getMainLooper());

        // Initialize database and load data
        initializeDatabase();

        // Update ad visibility
        updateAdVisibility();
    }

    private void initializeViews() {
        // Main views - NATIVE COMPONENTS ONLY
        recyclerView = findViewById(R.id.recyclerView);
        progressBar = findViewById(R.id.progressBar);

        // Use existing IDs from layout
        searchEditText = findViewById(R.id.searchText);
        btnSearch = findViewById(R.id.btnSearch);

        // Bottom navigation buttons
        btnSearch = findViewById(R.id.btnSearch);
        btnHome = findViewById(R.id.btnHome);
        btnBookmark = findViewById(R.id.btnBookmark);
        btnShare = findViewById(R.id.btnShare);
        btnSettings = findViewById(R.id.btnSettings);

        // Ad views
        adContainer = findViewById(R.id.adContainer);
        
        // Create banner ad
        adView = new AdView(this);
        adView.setAdUnitId(getString(R.string.admob_banner_id));
        adView.setAdSize(com.google.android.gms.ads.AdSize.BANNER);
        
        // Add AdView to container
        if (adContainer != null && adView != null) {
            LinearLayout adViewContainer = adContainer.findViewById(R.id.adViewContainer);
            if (adViewContainer != null) {
                adViewContainer.removeAllViews();
                adViewContainer.addView(adView);
                adViewContainer.setVisibility(View.VISIBLE);
            }
        }

        // Disclaimer banner
        disclaimerBanner = findViewById(R.id.disclaimerBanner);

        // Set initial visibility
        if (adContainer != null) {
            adContainer.setVisibility(View.GONE);
        }
        if (searchContainer != null) {
            searchContainer.setVisibility(View.GONE);
        }
        if (disclaimerBanner != null) {
            disclaimerBanner.setVisibility(View.VISIBLE);
        }
    }

    private void initializeViewModel() {
        viewModel = new ViewModelProvider(this).get(MainViewModel.class);
    }

    private void setupToolbarAndNavigation() {
        // Setup toolbar
        setSupportActionBar(toolbar);

        // Setup navigation drawer
        drawerToggle = new ActionBarDrawerToggle(
            this, drawerLayout, toolbar,
            R.string.navigation_drawer_open,
            R.string.navigation_drawer_close
        );
        drawerLayout.addDrawerListener(drawerToggle);
        drawerToggle.syncState();

        // Setup navigation view
        navigationView.setNavigationItemSelectedListener(this);
    }

    private void setupRecyclerView() {
        // NATIVE RECYCLERVIEW ADAPTER - NO WEBVIEW
        adapter = new LawContentAdapter(this, new LawContentAdapter.OnItemClickListener() {
            @Override
            public void onItemClick(LawEntity lawItem) {
                handleLawItemClick(lawItem);
            }

            @Override
            public void onItemLongClick(LawEntity lawItem) {
                handleLawItemLongClick(lawItem);
            }
        });

        recyclerView.setLayoutManager(new LinearLayoutManager(this));
        recyclerView.setAdapter(adapter);
    }

    private void setupConnectivityReceiver() {
        connectivityReceiver = new BroadcastReceiver() {
            @Override
            public void onReceive(Context context, Intent intent) {
                if (ConnectivityManager.CONNECTIVITY_ACTION.equals(intent.getAction())) {
                    updateAdVisibility();
                }
            }
        };
        IntentFilter filter = new IntentFilter(ConnectivityManager.CONNECTIVITY_ACTION);
        registerReceiver(connectivityReceiver, filter);
    }

    private void setupEventListeners() {
        // NATIVE SEARCH FUNCTIONALITY - NO WEBVIEW SEARCH
        if (btnSearch != null) {
            btnSearch.setOnClickListener(v -> toggleSearchView());
        }

        // Search EditText listeners - NATIVE TEXT SEARCH
        if (searchEditText != null) {
            searchEditText.addTextChangedListener(new TextWatcher() {
                @Override
                public void beforeTextChanged(CharSequence s, int start, int count, int after) {}

                @Override
                public void onTextChanged(CharSequence s, int start, int before, int count) {
                    // Cancel previous search
                    if (searchRunnable != null) {
                        searchHandler.removeCallbacks(searchRunnable);
                    }

                    String query = s.toString().trim();

                    // Show/hide buttons based on text
                    if (query.isEmpty()) {
                        if (searchButton != null) searchButton.setVisibility(View.GONE);
                        if (searchClear != null) searchClear.setVisibility(View.GONE);
                        if (viewModel != null) viewModel.navigateToHome();
                        if (adapter != null) adapter.setSearchQuery("");
                    } else {
                        if (searchButton != null) searchButton.setVisibility(View.VISIBLE);
                        if (searchClear != null) searchClear.setVisibility(View.VISIBLE);

                        // Debounce search with delay - NATIVE SEARCH
                        searchRunnable = () -> {
                            if (adapter != null) adapter.setSearchQuery(query);
                            if (viewModel != null) viewModel.loadAllContent();
                        };
                        searchHandler.postDelayed(searchRunnable, SEARCH_DELAY_MS);
                    }
                }

                @Override
                public void afterTextChanged(Editable s) {}
            });
        }

        // Search button click - NATIVE SEARCH
        if (searchButton != null) {
            searchButton.setOnClickListener(v -> {
                String query = searchEditText.getText().toString().trim();
                if (!query.isEmpty()) {
                    performNativeSearch(query);
                }
            });
        }

        // Clear button click
        if (searchClear != null) {
            searchClear.setOnClickListener(v -> {
                searchEditText.setText("");
                if (viewModel != null) viewModel.navigateToHome();
                if (adapter != null) adapter.setSearchQuery("");
            });
        }

        // Bottom navigation - ALL NATIVE
        if (btnHome != null) {
            btnHome.setOnClickListener(v -> {
                if (viewModel != null) viewModel.navigateToHome();
                if (adapter != null) adapter.setSearchQuery("");
                if (searchContainer != null && searchContainer.getVisibility() == View.VISIBLE) {
                    toggleSearchView();
                }
                recyclerView.scrollToPosition(0);
            });
        }

        if (btnBookmark != null) {
            btnBookmark.setOnClickListener(v -> toggleBookmark());
        }
        if (btnShare != null) {
            btnShare.setOnClickListener(v -> shareCurrentContent());
        }
        if (btnSettings != null) {
            btnSettings.setOnClickListener(v -> openSettings());
        }
    }

    private void setupObservers() {
        // Observe current content - NATIVE DATA BINDING
        if (viewModel != null) {
            viewModel.getCurrentContent().observe(this, lawItems -> {
                if (lawItems != null && adapter != null) {
                    adapter.setLawItems(lawItems);
                    if (progressBar != null) progressBar.setVisibility(View.GONE);
                }
            });

            // Observe loading state
            viewModel.isLoading().observe(this, isLoading -> {
                if (progressBar != null) {
                    progressBar.setVisibility(isLoading ? View.VISIBLE : View.GONE);
                }
            });

            // Observe error messages
            viewModel.getErrorMessage().observe(this, error -> {
                if (error != null && !error.isEmpty()) {
                    showError(error);
                    viewModel.clearError();
                }
            });

            // Observe current chapter
            viewModel.getCurrentChapter().observe(this, chapterNumber -> {
                updateToolbarTitle(chapterNumber);
            });
        }
    }

    private void initializeDatabase() {
        // NATIVE DATABASE INITIALIZATION - NO WEBVIEW
        if (progressBar != null) progressBar.setVisibility(View.VISIBLE);

        if (executor != null) {
            executor.execute(() -> {
                LawRepository repository = new LawRepository(this);
                repository.checkAndInitializeDatabase(isEmpty -> {
                    if (isEmpty) {
                        loadDataFromJson();
                    } else {
                        runOnUiThread(() -> {
                            if (viewModel != null) {
                                viewModel.initializeDatabase();
                                viewModel.loadAllContent();
                            }
                            if (progressBar != null) progressBar.setVisibility(View.GONE);
                        });
                    }
                });
            });
        }
    }

    private void loadDataFromJson() {
        // NATIVE JSON DATA LOADING - NO WEBVIEW
        if (executor != null) {
            executor.execute(() -> {
                try {
                    JsonDataLoader loader = new JsonDataLoader(this);
                    List<LawEntity> lawEntities = loader.loadLawDataFromAssets();

                    if (loader.validateData(lawEntities)) {
                        LawRepository repository = new LawRepository(this);
                        repository.insertLawItems(lawEntities);

                        runOnUiThread(() -> {
                            if (viewModel != null) {
                                viewModel.initializeDatabase();
                                viewModel.loadAllContent();
                            }
                            showSnackbar("Đã tải dữ liệu thành công!");
                            if (progressBar != null) progressBar.setVisibility(View.GONE);
                        });
                    } else {
                        runOnUiThread(() -> {
                            if (viewModel != null) {
                                viewModel.setError("Không thể tải dữ liệu từ file JSON");
                            }
                            if (progressBar != null) progressBar.setVisibility(View.GONE);
                        });
                    }
                } catch (Exception e) {
                    runOnUiThread(() -> {
                        if (viewModel != null) {
                            viewModel.setError("Lỗi khi tải dữ liệu: " + e.getMessage());
                        }
                        if (progressBar != null) progressBar.setVisibility(View.GONE);
                    });
                }
            });
        }
    }

    private void handleLawItemClick(LawEntity lawItem) {
        // NATIVE ITEM CLICK HANDLING - NO WEBVIEW NAVIGATION
        navigateToLawItem(lawItem);
    }

    private void navigateToLawItem(LawEntity lawItem) {
        // NATIVE NAVIGATION - NO WEBVIEW
        if (viewModel != null) {
            viewModel.setCurrentLawItem(lawItem.getId());

            if (lawItem.isChapter()) {
                viewModel.setCurrentChapter(lawItem.getNumber());
            } else if (lawItem.isArticle()) {
                viewModel.setCurrentArticle(lawItem.getNumber());
            }
        }
    }

    private void handleLawItemLongClick(LawEntity lawItem) {
        // NATIVE BOOKMARK HANDLING - NO WEBVIEW
        if (viewModel != null) {
            viewModel.setCurrentLawItem(lawItem.getId());
            viewModel.toggleBookmark(lawItem);
        }
        showSnackbar("Đã thêm/xóa bookmark");
    }

    private void performNativeSearch(String query) {
        // NATIVE SEARCH - NO WEBVIEW SEARCH
        if (viewModel != null) {
            viewModel.performSearch(query);
        }
        Toast.makeText(this, "Tìm kiếm: " + query, Toast.LENGTH_SHORT).show();
    }

    private void toggleSearchView() {
        // NATIVE SEARCH UI TOGGLE - NO WEBVIEW
        if (searchContainer != null) {
            if (searchContainer.getVisibility() == View.VISIBLE) {
                searchContainer.setVisibility(View.GONE);
                if (searchEditText != null) searchEditText.setText("");
                if (toolbar != null) toolbar.setTitle(getString(R.string.app_name));
                if (viewModel != null) viewModel.navigateToHome();
                if (adapter != null) adapter.setSearchQuery("");
            } else {
                searchContainer.setVisibility(View.VISIBLE);
                if (searchEditText != null) searchEditText.requestFocus();
                if (toolbar != null) toolbar.setTitle("");
            }
        }
    }

    private void toggleBookmark() {
        Toast.makeText(this, "Bookmark clicked", Toast.LENGTH_SHORT).show();
    }

    private void shareCurrentContent() {
        // NATIVE SHARE - NO WEBVIEW URL SHARING
        Toast.makeText(this, "Share clicked", Toast.LENGTH_SHORT).show();
    }

    private void openSettings() {
        Toast.makeText(this, "Settings clicked", Toast.LENGTH_SHORT).show();
    }

    private void updateAdVisibility() {
        runOnUiThread(() -> {
            boolean isConnected = isNetworkAvailable();
            if (!isConnected) {
                if (adContainer != null) {
                    adContainer.setVisibility(View.GONE);
                }
            } else {
                loadAd();
            }
        });
    }

    private void loadAd() {
        if (isNetworkAvailable() && adView != null) {
            AdRequest adRequest = new AdRequest.Builder().build();
            adView.loadAd(adRequest);

            adView.setAdListener(new com.google.android.gms.ads.AdListener() {
                @Override
                public void onAdFailedToLoad(com.google.android.gms.ads.LoadAdError adError) {
                    super.onAdFailedToLoad(adError);
                    if (adContainer != null) {
                        adContainer.setVisibility(View.GONE);
                    }
                }

                @Override
                public void onAdLoaded() {
                    super.onAdLoaded();
                    if (adContainer != null) {
                        adContainer.setVisibility(View.VISIBLE);
                    }
                }
            });
        } else {
            if (adContainer != null) {
                adContainer.setVisibility(View.GONE);
            }
        }
    }

    private boolean isNetworkAvailable() {
        ConnectivityManager connectivityManager = (ConnectivityManager) getSystemService(Context.CONNECTIVITY_SERVICE);
        if (connectivityManager != null) {
            NetworkInfo activeNetworkInfo = connectivityManager.getActiveNetworkInfo();
            return activeNetworkInfo != null && activeNetworkInfo.isConnected();
        }
        return false;
    }

    private void showError(String message) {
        Toast.makeText(this, message, Toast.LENGTH_LONG).show();
    }

    private void showSnackbar(String message) {
        if (recyclerView != null) {
            Snackbar.make(recyclerView, message, Snackbar.LENGTH_SHORT).show();
        }
    }

    private void updateToolbarTitle(String chapterNumber) {
        if (toolbar != null && chapterNumber != null) {
            toolbar.setTitle("Chương " + chapterNumber);
        }
    }

    @Override
    public boolean onNavigationItemSelected(@NonNull MenuItem item) {
        // Handle navigation view item clicks here - NATIVE NAVIGATION
        drawerLayout.closeDrawer(GravityCompat.START);
        return true;
    }

    @Override
    public void onBackPressed() {
        // NATIVE BACK HANDLING - NO WEBVIEW BACK NAVIGATION
        if (drawerLayout.isDrawerOpen(GravityCompat.START)) {
            drawerLayout.closeDrawer(GravityCompat.START);
        } else {
            super.onBackPressed();
        }
    }

    @Override
    protected void onDestroy() {
        // CLEANUP - NO WEBVIEW CLEANUP NEEDED
        if (connectivityReceiver != null) {
            unregisterReceiver(connectivityReceiver);
        }
        if (adView != null) {
            adView.destroy();
        }
        if (executor != null) {
            executor.shutdown();
        }
        super.onDestroy();
    }
}

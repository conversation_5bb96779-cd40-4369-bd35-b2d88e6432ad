package com.oriondev.luatdansu;

import android.app.SearchManager;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.SharedPreferences;
import android.net.ConnectivityManager;
import android.net.NetworkInfo;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.text.Editable;
import android.text.TextWatcher;
import android.view.MenuItem;
import android.view.View;
import android.view.inputmethod.EditorInfo;
import android.widget.EditText;
import android.widget.ImageButton;
import android.widget.LinearLayout;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.appcompat.app.ActionBarDrawerToggle;
import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.widget.SearchView;
import androidx.core.splashscreen.SplashScreen;
import androidx.core.view.GravityCompat;
import androidx.core.view.WindowCompat;
import androidx.drawerlayout.widget.DrawerLayout;
import androidx.lifecycle.ViewModelProvider;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.google.android.gms.ads.AdRequest;
import com.google.android.gms.ads.AdView;
import com.google.android.gms.ads.MobileAds;
import com.google.android.material.appbar.MaterialToolbar;
import com.google.android.material.button.MaterialButton;
import com.google.android.material.card.MaterialCardView;
import com.google.android.material.navigation.NavigationView;
import com.google.android.material.progressindicator.LinearProgressIndicator;
import com.google.android.material.snackbar.Snackbar;

import com.oriondev.luatdansu.adapter.LawContentAdapter;
import com.oriondev.luatdansu.database.entity.LawEntity;
import com.oriondev.luatdansu.repository.LawRepository;
import com.oriondev.luatdansu.utils.JsonDataLoader;
import com.oriondev.luatdansu.viewmodel.MainViewModel;

import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

public class MainActivity extends AppCompatActivity implements NavigationView.OnNavigationItemSelectedListener {

    // UI Components
    private RecyclerView recyclerView;
    private LinearLayout searchContainer;
    private EditText searchEditText;
    private ImageButton searchButton, searchClear;
    private MaterialButton btnSearch, btnHome, btnBookmark, btnShare, btnSettings;
    private AdView adView;
    private LinearProgressIndicator progressBar;
    private MaterialCardView adContainer;
    private MaterialToolbar toolbar;
    private DrawerLayout drawerLayout;
    private NavigationView navigationView;
    private ActionBarDrawerToggle drawerToggle;

    // Data and Logic
    private MainViewModel viewModel;
    private LawContentAdapter adapter;
    private BroadcastReceiver connectivityReceiver;
    private SharedPreferences preferences;
    private ExecutorService executor;

    // Search optimization
    private Handler searchHandler;
    private Runnable searchRunnable;
    private static final int SEARCH_DELAY_MS = 300;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        // Handle the splash screen transition
        SplashScreen splashScreen = SplashScreen.installSplashScreen(this);

        super.onCreate(savedInstanceState);

        // Enable edge-to-edge display
        WindowCompat.setDecorFitsSystemWindows(getWindow(), false);

        setContentView(R.layout.activity_main);

        // Initialize components
        initializeViews();
        initializeViewModel();
        setupToolbarAndNavigation();
        setupRecyclerView();
        setupConnectivityReceiver();
        setupEventListeners();
        setupObservers();

        // Initialize AdMob SDK
        MobileAds.initialize(this, initializationStatus -> {});

        // Initialize SharedPreferences
        preferences = getSharedPreferences("app_settings", MODE_PRIVATE);
        executor = Executors.newFixedThreadPool(2);

        // Initialize search handler
        searchHandler = new Handler(Looper.getMainLooper());

        // Initialize database and load data
        initializeDatabase();

        // Update ad visibility
        updateAdVisibility();
    }

    private void initializeViews() {
        // Main views
        recyclerView = findViewById(R.id.recyclerView);
        toolbar = findViewById(R.id.toolbar);
        drawerLayout = findViewById(R.id.drawer_layout);
        navigationView = findViewById(R.id.navigation_view);
        progressBar = findViewById(R.id.progressBar);

        // Search views
        searchContainer = findViewById(R.id.search_container);
        searchEditText = findViewById(R.id.search_edit_text);
        searchButton = findViewById(R.id.search_button);
        searchClear = findViewById(R.id.search_clear);

        // Bottom navigation buttons
        btnSearch = findViewById(R.id.btnSearch);
        btnHome = findViewById(R.id.btnHome);
        btnBookmark = findViewById(R.id.btnBookmark);
        btnShare = findViewById(R.id.btnShare);
        btnSettings = findViewById(R.id.btnSettings);

        // Ad views
        adView = findViewById(R.id.adView);
        adContainer = findViewById(R.id.adContainer);

        // Set initial visibility
        if (adContainer != null) {
            adContainer.setVisibility(View.GONE);
        }
        if (searchContainer != null) {
            searchContainer.setVisibility(View.GONE);
        }
    }



    private void initializeViewModel() {
        viewModel = new ViewModelProvider(this).get(MainViewModel.class);
    }

    private void setupToolbarAndNavigation() {
        // Setup toolbar
        setSupportActionBar(toolbar);

        // Setup navigation drawer
        drawerToggle = new ActionBarDrawerToggle(
            this, drawerLayout, toolbar,
            R.string.navigation_drawer_open,
            R.string.navigation_drawer_close
        );
        drawerLayout.addDrawerListener(drawerToggle);
        drawerToggle.syncState();

        // Setup navigation view
        navigationView.setNavigationItemSelectedListener(this);
    }

    private void setupRecyclerView() {
        adapter = new LawContentAdapter(this, new LawContentAdapter.OnItemClickListener() {
            @Override
            public void onItemClick(LawEntity lawItem) {
                handleLawItemClick(lawItem);
            }

            @Override
            public void onItemLongClick(LawEntity lawItem) {
                handleLawItemLongClick(lawItem);
            }
        });

        recyclerView.setLayoutManager(new LinearLayoutManager(this));
        recyclerView.setAdapter(adapter);
    }

    private void setupConnectivityReceiver() {
        connectivityReceiver = new BroadcastReceiver() {
            @Override
            public void onReceive(Context context, Intent intent) {
                if (ConnectivityManager.CONNECTIVITY_ACTION.equals(intent.getAction())) {
                    updateAdVisibility();
                }
            }
        };
        IntentFilter filter = new IntentFilter(ConnectivityManager.CONNECTIVITY_ACTION);
        registerReceiver(connectivityReceiver, filter);
    }

    private void setupEventListeners() {
        // Search functionality
        btnSearch.setOnClickListener(v -> toggleSearchView());

        // Search EditText listeners
        searchEditText.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {}

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
                // Cancel previous search
                if (searchRunnable != null) {
                    searchHandler.removeCallbacks(searchRunnable);
                }

                String query = s.toString().trim();

                // Show/hide buttons based on text
                if (query.isEmpty()) {
                    searchButton.setVisibility(View.GONE);
                    searchClear.setVisibility(View.GONE);
                    viewModel.navigateToHome();
                    adapter.setSearchQuery("");
                } else {
                    searchButton.setVisibility(View.VISIBLE);
                    searchClear.setVisibility(View.VISIBLE);

                    // Debounce search with delay
                    searchRunnable = () -> {
                        adapter.setSearchQuery(query);
                        viewModel.loadAllContent();
                    };
                    searchHandler.postDelayed(searchRunnable, SEARCH_DELAY_MS);
                }
            }

            @Override
            public void afterTextChanged(Editable s) {}
        });

        // Search button click
        searchButton.setOnClickListener(v -> {
            String query = searchEditText.getText().toString().trim();
            if (!query.isEmpty()) {
                performSearch(query);
            }
        });

        // Clear button click
        searchClear.setOnClickListener(v -> {
            searchEditText.setText("");
            viewModel.navigateToHome();
            adapter.setSearchQuery("");
        });

        // Handle keyboard search action
        searchEditText.setOnEditorActionListener((v, actionId, event) -> {
            if (actionId == EditorInfo.IME_ACTION_SEARCH) {
                String query = searchEditText.getText().toString().trim();
                if (!query.isEmpty()) {
                    performSearch(query);
                }
                return true;
            }
            return false;
        });

        // Bottom navigation
        btnHome.setOnClickListener(v -> {
            viewModel.navigateToHome();
            adapter.setSearchQuery("");
            if (searchContainer.getVisibility() == View.VISIBLE) {
                toggleSearchView();
            }
            recyclerView.scrollToPosition(0);
        });

        btnBookmark.setOnClickListener(v -> toggleBookmark());
        btnShare.setOnClickListener(v -> shareCurrentContent());
        btnSettings.setOnClickListener(v -> openSettings());
    }

    private void setupObservers() {
        // Observe current content
        viewModel.getCurrentContent().observe(this, lawItems -> {
            if (lawItems != null) {
                adapter.setLawItems(lawItems);
                progressBar.setVisibility(View.GONE);
            }
        });

        // Observe loading state - DISABLED
        viewModel.isLoading().observe(this, isLoading -> {
            // Loading indicator disabled per user request
            progressBar.setVisibility(View.GONE);
        });

        // Observe error messages
        viewModel.getErrorMessage().observe(this, error -> {
            if (error != null && !error.isEmpty()) {
                showError(error);
                viewModel.clearError();
            }
        });

        // Observe current chapter
        viewModel.getCurrentChapter().observe(this, chapterNumber -> {
            updateToolbarTitle(chapterNumber);
        });
    }

    private void initializeDatabase() {
        // Progress bar disabled per user request
        progressBar.setVisibility(View.GONE);

        executor.execute(() -> {
            LawRepository repository = new LawRepository(this);
            repository.checkAndInitializeDatabase(isEmpty -> {
                if (isEmpty) {
                    loadDataFromJson();
                } else {
                    runOnUiThread(() -> {
                        viewModel.initializeDatabase();
                        viewModel.loadAllContent();
                        // Keep progress bar hidden
                        progressBar.setVisibility(View.GONE);
                    });
                }
            });
        });
    }

    private void loadDataFromJson() {
        executor.execute(() -> {
            try {
                // Test JSON loading first
                TestJsonLoader.testJsonLoading(this);

                JsonDataLoader loader = new JsonDataLoader(this);
                List<LawEntity> lawEntities = loader.loadLawDataFromAssets();

                if (loader.validateData(lawEntities)) {
                    LawRepository repository = new LawRepository(this);
                    repository.insertLawItems(lawEntities);

                    runOnUiThread(() -> {
                        viewModel.initializeDatabase();
                        viewModel.loadAllContent();
                        showSnackbar("Đã tải dữ liệu thành công!");
                    });
                } else {
                    runOnUiThread(() -> {
                        viewModel.setError("Không thể tải dữ liệu từ file JSON");
                    });
                }
            } catch (Exception e) {
                runOnUiThread(() -> {
                    viewModel.setError("Lỗi khi tải dữ liệu: " + e.getMessage());
                });
            }
        });
    }

    private void forceReloadDataFromJson() {
        runOnUiThread(() -> {
            showSnackbar("Đang tải lại dữ liệu từ JSON...");
            progressBar.setVisibility(View.VISIBLE);
        });

        executor.execute(() -> {
            try {
                LawRepository repository = new LawRepository(this);

                // Clear existing data first
                repository.deleteAllLawItems();

                // Wait a bit for deletion to complete
                Thread.sleep(500);

                // Test JSON loading first
                TestJsonLoader.testJsonLoading(this);

                JsonDataLoader loader = new JsonDataLoader(this);
                List<LawEntity> lawEntities = loader.loadLawDataFromAssets();

                if (loader.validateData(lawEntities)) {
                    repository.insertLawItems(lawEntities);

                    runOnUiThread(() -> {
                        progressBar.setVisibility(View.GONE);
                        viewModel.initializeDatabase();
                        viewModel.loadAllContent();
                        showSnackbar("✅ Đã tải lại dữ liệu thành công từ JSON!");
                    });
                } else {
                    runOnUiThread(() -> {
                        progressBar.setVisibility(View.GONE);
                        viewModel.setError("❌ Không thể tải dữ liệu từ file JSON");
                    });
                }
            } catch (Exception e) {
                runOnUiThread(() -> {
                    progressBar.setVisibility(View.GONE);
                    viewModel.setError("❌ Lỗi khi tải dữ liệu: " + e.getMessage());
                });
            }
        });
    }

    private void handleLawItemClick(LawEntity lawItem) {
        viewModel.setCurrentLawItem(lawItem.getId());

        if (lawItem.isChapter()) {
            viewModel.setCurrentChapter(lawItem.getNumber());
        } else if (lawItem.isArticle()) {
            viewModel.setCurrentArticle(lawItem.getNumber());
        }
    }

    private void handleLawItemLongClick(LawEntity lawItem) {
        viewModel.setCurrentLawItem(lawItem.getId());
        viewModel.toggleBookmark(lawItem);

        String message = "Đã thêm/xóa bookmark";
        showSnackbar(message);
    }

    private void performSearch(String query) {
        Intent searchIntent = new Intent(this, SearchActivity.class);
        searchIntent.setAction(Intent.ACTION_SEARCH);
        searchIntent.putExtra(SearchManager.QUERY, query);
        startActivityForResult(searchIntent, 200);
    }

    private void toggleSearchView() {
        if (searchContainer.getVisibility() == View.VISIBLE) {
            searchContainer.setVisibility(View.GONE);
            searchEditText.setText("");
            toolbar.setTitle(getString(R.string.app_name));
            viewModel.navigateToHome();
            adapter.setSearchQuery("");
        } else {
            searchContainer.setVisibility(View.VISIBLE);
            searchEditText.requestFocus();
            toolbar.setTitle("");
        }
    }

    private void toggleBookmark() {
        Intent intent = new Intent(this, BookmarkActivity.class);
        startActivityForResult(intent, 300);
    }

    private void shareCurrentContent() {
        Intent shareIntent = new Intent(Intent.ACTION_SEND);
        shareIntent.setType("text/plain");
        shareIntent.putExtra(Intent.EXTRA_TEXT,
            getString(R.string.share_article_text) + "\n" + getString(R.string.app_name));
        startActivity(Intent.createChooser(shareIntent, getString(R.string.action_share)));
        showSnackbar("Đã chia sẻ");
    }

    private void openSettings() {
        drawerLayout.openDrawer(GravityCompat.START);
    }

    private void updateToolbarTitle(String chapterNumber) {
        if (chapterNumber != null && !chapterNumber.isEmpty()) {
            toolbar.setTitle("Chương " + chapterNumber);
        } else {
            toolbar.setTitle(getString(R.string.app_name));
        }
    }

    private void updateAdVisibility() {
        runOnUiThread(() -> {
            boolean isConnected = isNetworkAvailable();
            if (!isConnected) {
                if (adContainer != null) {
                    adContainer.setVisibility(View.GONE);
                }
            } else {
                loadAd();
            }
        });
    }

    private void loadAd() {
        if (isNetworkAvailable() && adView != null) {
            AdRequest adRequest = new AdRequest.Builder().build();
            adView.loadAd(adRequest);

            adView.setAdListener(new com.google.android.gms.ads.AdListener() {
                @Override
                public void onAdFailedToLoad(com.google.android.gms.ads.LoadAdError adError) {
                    super.onAdFailedToLoad(adError);
                    if (adContainer != null) {
                        adContainer.setVisibility(View.GONE);
                    }
                }

                @Override
                public void onAdLoaded() {
                    super.onAdLoaded();
                    if (adContainer != null) {
                        adContainer.setVisibility(View.VISIBLE);
                    }
                }
            });
        } else {
            if (adContainer != null) {
                adContainer.setVisibility(View.GONE);
            }
        }
    }

    private boolean isNetworkAvailable() {
        ConnectivityManager connectivityManager = (ConnectivityManager) getSystemService(Context.CONNECTIVITY_SERVICE);
        if (connectivityManager != null) {
            NetworkInfo activeNetworkInfo = connectivityManager.getActiveNetworkInfo();
            return activeNetworkInfo != null && activeNetworkInfo.isConnected();
        }
        return false;
    }

    private void showSnackbar(String message) {
        Snackbar.make(findViewById(android.R.id.content), message, Snackbar.LENGTH_SHORT).show();
    }

    private void showError(String message) {
        Toast.makeText(this, message, Toast.LENGTH_LONG).show();
    }

    @Override
    public boolean onNavigationItemSelected(@NonNull MenuItem item) {
        int id = item.getItemId();

        if (id == R.id.nav_home) {
            viewModel.navigateToHome();
            adapter.setSearchQuery("");
            if (searchContainer.getVisibility() == View.VISIBLE) {
                toggleSearchView();
            }
        } else if (id == R.id.nav_chapters) {
            Intent intent = new Intent(this, ChaptersActivity.class);
            startActivityForResult(intent, 100);
        } else if (id == R.id.nav_bookmarks) {
            Intent intent = new Intent(this, BookmarkActivity.class);
            startActivityForResult(intent, 300);
        } else if (id == R.id.nav_history) {
            showSnackbar("Lịch sử tìm kiếm - Sẽ được cập nhật");
        } else if (id == R.id.nav_text_size) {
            showSnackbar("Điều chỉnh kích thước chữ - Sẽ được cập nhật");
        } else if (id == R.id.nav_dark_mode) {
            showSnackbar("Chế độ tối - Sẽ được cập nhật");
        } else if (id == R.id.nav_settings) {
            showSnackbar("Cài đặt - Sẽ được cập nhật");
        } else if (id == R.id.nav_reload_data) {
            forceReloadDataFromJson();
        } else if (id == R.id.nav_share_app) {
            shareApp();
        } else if (id == R.id.nav_privacy) {
            showSnackbar("Chính sách bảo mật - Sẽ được cập nhật");
        }

        drawerLayout.closeDrawer(GravityCompat.START);
        return true;
    }

    private void shareApp() {
        Intent shareIntent = new Intent(Intent.ACTION_SEND);
        shareIntent.setType("text/plain");
        shareIntent.putExtra(Intent.EXTRA_TEXT, getString(R.string.share_app_text));
        startActivity(Intent.createChooser(shareIntent, getString(R.string.menu_share_app)));
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);

        if (resultCode == RESULT_OK && data != null) {
            if (requestCode == 100) {
                // From ChaptersActivity
                String anchor = data.getStringExtra("chapter_anchor");
                if (anchor != null) {
                    String chapterNumber = anchor.replace("#chuong_", "");
                    viewModel.setCurrentChapter(chapterNumber);
                }
            } else if (requestCode == 200) {
                // From SearchActivity
                handleSearchResult(data);
            } else if (requestCode == 300) {
                // From BookmarkActivity
                handleBookmarkResult(data);
            }
        }
    }

    private void handleSearchResult(Intent data) {
        int lawItemId = data.getIntExtra("selected_law_item_id", -1);
        String chapterNumber = data.getStringExtra("chapter_number");
        String articleNumber = data.getStringExtra("article_number");

        if (lawItemId != -1) {
            viewModel.setCurrentLawItem(lawItemId);
        }
        if (chapterNumber != null) {
            viewModel.setCurrentChapter(chapterNumber);
        }
        if (articleNumber != null) {
            viewModel.setCurrentArticle(articleNumber);
        }
    }

    private void handleBookmarkResult(Intent data) {
        int lawItemId = data.getIntExtra("selected_law_item_id", -1);
        String chapterNumber = data.getStringExtra("chapter_number");
        String articleNumber = data.getStringExtra("article_number");

        if (lawItemId != -1) {
            viewModel.setCurrentLawItem(lawItemId);
        }
        if (chapterNumber != null) {
            viewModel.setCurrentChapter(chapterNumber);
        }
        if (articleNumber != null) {
            viewModel.setCurrentArticle(articleNumber);
        }

        showSnackbar("Đã chuyển đến bookmark");
    }

    @Override
    public void onBackPressed() {
        if (drawerLayout.isDrawerOpen(GravityCompat.START)) {
            drawerLayout.closeDrawer(GravityCompat.START);
        } else if (searchContainer.getVisibility() == View.VISIBLE) {
            toggleSearchView();
        } else {
            super.onBackPressed();
        }
    }

    @Override
    protected void onResume() {
        super.onResume();
        updateAdVisibility();

        getWindow().getDecorView().setSystemUiVisibility(
            View.SYSTEM_UI_FLAG_LAYOUT_STABLE |
            View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
        );
    }

    @Override
    protected void onDestroy() {
        if (connectivityReceiver != null) {
            unregisterReceiver(connectivityReceiver);
        }
        if (executor != null && !executor.isShutdown()) {
            executor.shutdown();
        }
        super.onDestroy();
    }



    @Override
    public void onConfigurationChanged(android.content.res.Configuration newConfig) {
        super.onConfigurationChanged(newConfig);
        if (adView != null) {
            loadAd();
        }
    }
}

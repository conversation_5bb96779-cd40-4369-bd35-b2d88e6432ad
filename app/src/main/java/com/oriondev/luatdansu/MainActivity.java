package com.oriondev.luatdansu;

import android.os.Bundle;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.widget.SearchView;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.google.android.gms.ads.AdRequest;
import com.google.android.gms.ads.AdView;
import com.google.android.gms.ads.MobileAds;
import com.google.android.material.button.MaterialButton;
import com.google.android.material.card.MaterialCardView;
import com.google.android.material.floatingactionbutton.FloatingActionButton;
import com.google.android.material.progressindicator.LinearProgressIndicator;

public class MainActivity extends AppCompatActivity {

    // UI Components
    private RecyclerView recyclerView;
    private LinearLayout searchContainer;
    private SearchView searchView;
    private MaterialButton btnSearch, btnHome, btnBookmark, btnShare, btnSettings;
    private FloatingActionButton fabSearch;
    private AdView adView;
    private LinearProgressIndicator progressBar;
    private MaterialCardView adContainer;
    private MaterialCardView disclaimerBanner;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_main);

        // Initialize AdMob SDK
        MobileAds.initialize(this, initializationStatus -> {
            // AdMob SDK initialized
        });

        // Initialize views
        initializeViews();
        setupEventListeners();
    }

    private void initializeViews() {
        try {
            // Main views
            recyclerView = findViewById(R.id.recyclerView);
            progressBar = findViewById(R.id.progressBar);

            // Search views - Updated for new layout
            searchContainer = findViewById(R.id.searchContainer);
            searchView = findViewById(R.id.search_view);

            // Bottom navigation buttons
            btnSearch = findViewById(R.id.btnSearch);
            btnHome = findViewById(R.id.btnHome);
            btnBookmark = findViewById(R.id.btnBookmark);
            btnShare = findViewById(R.id.btnShare);
            btnSettings = findViewById(R.id.btnSettings);
            
            // Floating Action Button
            fabSearch = findViewById(R.id.fab_search);

            // Initialize beautiful ad views
            adContainer = findViewById(R.id.adContainer);
            LinearLayout adViewContainer = findViewById(R.id.adViewContainer);

            // Create banner ad with beautiful styling
            if (adViewContainer != null) {
                adView = new AdView(this);
                adView.setAdUnitId(getString(R.string.admob_banner_id));
                adView.setAdSize(com.google.android.gms.ads.AdSize.BANNER);

                // Add AdView to beautiful container
                adViewContainer.addView(adView);
                
                // Load ad
                AdRequest adRequest = new AdRequest.Builder().build();
                adView.loadAd(adRequest);
            }

            // Disclaimer banner
            disclaimerBanner = findViewById(R.id.disclaimerBanner);

            // Set initial visibility
            if (adContainer != null) {
                adContainer.setVisibility(View.GONE);
            }
            if (searchContainer != null) {
                searchContainer.setVisibility(View.GONE);
            }
            if (disclaimerBanner != null) {
                disclaimerBanner.setVisibility(View.VISIBLE);
            }
            if (progressBar != null) {
                progressBar.setVisibility(View.GONE);
            }

            // Setup RecyclerView
            if (recyclerView != null) {
                recyclerView.setLayoutManager(new LinearLayoutManager(this));
            }

            Toast.makeText(this, "App initialized successfully!", Toast.LENGTH_SHORT).show();

        } catch (Exception e) {
            Toast.makeText(this, "Error initializing views: " + e.getMessage(), Toast.LENGTH_LONG).show();
        }
    }

    private void setupEventListeners() {
        try {
            // Search functionality with new SearchView
            if (btnSearch != null) {
                btnSearch.setOnClickListener(v -> toggleSearchView());
            }
            
            if (fabSearch != null) {
                fabSearch.setOnClickListener(v -> toggleSearchView());
            }

            // SearchView listeners
            if (searchView != null) {
                searchView.setOnQueryTextListener(new SearchView.OnQueryTextListener() {
                    @Override
                    public boolean onQueryTextSubmit(String query) {
                        if (!query.trim().isEmpty()) {
                            Toast.makeText(MainActivity.this, "Search: " + query, Toast.LENGTH_SHORT).show();
                        }
                        return true;
                    }

                    @Override
                    public boolean onQueryTextChange(String newText) {
                        // Handle search text change
                        return true;
                    }
                });
            }

            // Bottom navigation
            if (btnHome != null) {
                btnHome.setOnClickListener(v -> {
                    Toast.makeText(this, "Home clicked", Toast.LENGTH_SHORT).show();
                });
            }

            if (btnBookmark != null) {
                btnBookmark.setOnClickListener(v -> {
                    Toast.makeText(this, "Bookmark clicked", Toast.LENGTH_SHORT).show();
                });
            }

            if (btnShare != null) {
                btnShare.setOnClickListener(v -> {
                    Toast.makeText(this, "Share clicked", Toast.LENGTH_SHORT).show();
                });
            }

            if (btnSettings != null) {
                btnSettings.setOnClickListener(v -> {
                    Toast.makeText(this, "Settings clicked", Toast.LENGTH_SHORT).show();
                });
            }

        } catch (Exception e) {
            Toast.makeText(this, "Error setting up listeners: " + e.getMessage(), Toast.LENGTH_LONG).show();
        }
    }

    private void toggleSearchView() {
        if (searchContainer != null) {
            if (searchContainer.getVisibility() == View.VISIBLE) {
                searchContainer.setVisibility(View.GONE);
                if (searchView != null) {
                    searchView.setQuery("", false);
                    searchView.clearFocus();
                }
            } else {
                searchContainer.setVisibility(View.VISIBLE);
                if (searchView != null) {
                    searchView.requestFocus();
                }
            }
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (adView != null) {
            adView.destroy();
        }
    }
}

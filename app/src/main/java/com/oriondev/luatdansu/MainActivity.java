package com.oriondev.luatdansu;

import android.os.Bundle;
import android.view.View;
import android.widget.EditText;
import android.widget.ImageButton;
import android.widget.LinearLayout;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.google.android.gms.ads.AdRequest;
import com.google.android.gms.ads.AdView;
import com.google.android.gms.ads.MobileAds;
import com.google.android.material.button.MaterialButton;
import com.google.android.material.card.MaterialCardView;
import com.google.android.material.progressindicator.LinearProgressIndicator;

public class MainActivity extends AppCompatActivity {

    // UI Components
    private RecyclerView recyclerView;
    private LinearLayout searchContainer;
    private EditText searchEditText;
    private ImageButton searchButton, searchClear;
    private MaterialButton btnSearch, btnHome, btnBookmark, btnShare, btnSettings;
    private AdView adView;
    private LinearProgressIndicator progressBar;
    private MaterialCardView adContainer;
    private MaterialCardView disclaimerBanner;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_main);

        // Initialize AdMob SDK
        MobileAds.initialize(this, initializationStatus -> {
            // AdMob SDK initialized
        });

        // Initialize views
        initializeViews();
        setupEventListeners();
        
        Toast.makeText(this, "App started successfully!", Toast.LENGTH_SHORT).show();
    }

    private void initializeViews() {
        try {
            // Main views
            recyclerView = findViewById(R.id.recyclerView);
            progressBar = findViewById(R.id.progressBar);

            // Search views - Safe findViewById with null checks
            searchContainer = findViewById(R.id.search_container);
            searchEditText = findViewById(R.id.search_edit_text);
            
            // Safe casting for search buttons
            View searchButtonView = findViewById(R.id.search_button);
            View searchClearView = findViewById(R.id.search_clear);
            
            if (searchButtonView instanceof ImageButton) {
                searchButton = (ImageButton) searchButtonView;
            }
            if (searchClearView instanceof ImageButton) {
                searchClear = (ImageButton) searchClearView;
            }

            // Bottom navigation buttons
            btnSearch = findViewById(R.id.btnSearch);
            btnHome = findViewById(R.id.btnHome);
            btnBookmark = findViewById(R.id.btnBookmark);
            btnShare = findViewById(R.id.btnShare);
            btnSettings = findViewById(R.id.btnSettings);

            // Initialize ad views
            adContainer = findViewById(R.id.adContainer);
            LinearLayout adViewContainer = findViewById(R.id.adViewContainer);

            // Create banner ad
            if (adViewContainer != null) {
                adView = new AdView(this);
                adView.setAdUnitId(getString(R.string.admob_banner_id));
                adView.setAdSize(com.google.android.gms.ads.AdSize.BANNER);
                adViewContainer.addView(adView);
                
                // Load ad
                AdRequest adRequest = new AdRequest.Builder().build();
                adView.loadAd(adRequest);
            }

            // Disclaimer banner
            disclaimerBanner = findViewById(R.id.disclaimerBanner);

            // Set initial visibility
            if (adContainer != null) {
                adContainer.setVisibility(View.GONE);
            }
            if (searchContainer != null) {
                searchContainer.setVisibility(View.GONE);
            }
            if (disclaimerBanner != null) {
                disclaimerBanner.setVisibility(View.VISIBLE);
            }
            if (progressBar != null) {
                progressBar.setVisibility(View.GONE);
            }

            // Setup RecyclerView
            if (recyclerView != null) {
                recyclerView.setLayoutManager(new LinearLayoutManager(this));
            }

            Toast.makeText(this, "Views initialized successfully!", Toast.LENGTH_SHORT).show();

        } catch (Exception e) {
            Toast.makeText(this, "Error initializing views: " + e.getMessage(), Toast.LENGTH_LONG).show();
        }
    }

    private void setupEventListeners() {
        try {
            // Search functionality
            if (btnSearch != null) {
                btnSearch.setOnClickListener(v -> toggleSearchView());
            }

            // Search button click
            if (searchButton != null) {
                searchButton.setOnClickListener(v -> {
                    String query = searchEditText != null ? searchEditText.getText().toString().trim() : "";
                    if (!query.isEmpty()) {
                        Toast.makeText(this, "Search: " + query, Toast.LENGTH_SHORT).show();
                    }
                });
            }

            // Clear button click
            if (searchClear != null) {
                searchClear.setOnClickListener(v -> {
                    if (searchEditText != null) {
                        searchEditText.setText("");
                    }
                });
            }

            // Bottom navigation
            if (btnHome != null) {
                btnHome.setOnClickListener(v -> {
                    Toast.makeText(this, "Home clicked", Toast.LENGTH_SHORT).show();
                    if (searchContainer != null && searchContainer.getVisibility() == View.VISIBLE) {
                        toggleSearchView();
                    }
                    if (recyclerView != null) {
                        recyclerView.scrollToPosition(0);
                    }
                });
            }

            if (btnBookmark != null) {
                btnBookmark.setOnClickListener(v -> {
                    Toast.makeText(this, "Bookmark clicked", Toast.LENGTH_SHORT).show();
                });
            }

            if (btnShare != null) {
                btnShare.setOnClickListener(v -> {
                    Toast.makeText(this, "Share clicked", Toast.LENGTH_SHORT).show();
                });
            }

            if (btnSettings != null) {
                btnSettings.setOnClickListener(v -> {
                    Toast.makeText(this, "Settings clicked", Toast.LENGTH_SHORT).show();
                });
            }

        } catch (Exception e) {
            Toast.makeText(this, "Error setting up listeners: " + e.getMessage(), Toast.LENGTH_LONG).show();
        }
    }

    private void toggleSearchView() {
        if (searchContainer != null) {
            if (searchContainer.getVisibility() == View.VISIBLE) {
                searchContainer.setVisibility(View.GONE);
                if (searchEditText != null) {
                    searchEditText.setText("");
                    searchEditText.clearFocus();
                }
            } else {
                searchContainer.setVisibility(View.VISIBLE);
                if (searchEditText != null) {
                    searchEditText.requestFocus();
                }
            }
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (adView != null) {
            adView.destroy();
        }
    }
}
